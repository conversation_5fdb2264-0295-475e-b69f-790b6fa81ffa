/**
 * Enhanced Advanced Humanization Engine - Designed to achieve ≤10% AI detection
 * Now powered by state-of-the-art language models (DeepSeek-R1, Llama 3.1)
 * Maintains backward compatibility with pattern-based fallback system
 */

import {
    analyzeDocumentStructure,
    identifyProtectedSentences,
    calculateHesitationFrequency
} from './contentAnalyzer.js';

import {
    humanizeTextWithHF,
    testHFConnection,
    getHFConfiguration
} from '../services/huggingFaceClient.js';

import {
    humanizeWithAdvancedLLM,
    isAdvancedLLMAvailable
} from '../services/falconService.js';

/**
 * Main enhanced humanization function
 * Now powered by advanced language models with pattern-based fallback
 */
export async function advancedHumanization(text, options = {}) {
    // Input validation
    if (!text || typeof text !== 'string') {
        console.error('Invalid input: text must be a non-empty string', { text, type: typeof text });
        throw new Error('Input text must be a non-empty string');
    }

    if (!text.trim()) {
        console.warn('Empty text provided to humanization');
        return {
            text: '',
            method: 'empty-input',
            success: true,
            originalLength: 0,
            newLength: 0
        };
    }

    const {
        aggressiveness = 0.7, // 0-1 scale, higher = more changes
        maintainTone = true,
        targetDetection = 10, // Target AI detection percentage
        useModelBased = true, // Use LLM-based humanization (new default)
        fallbackToPatterns = true // Fallback to pattern-based if LLM fails
    } = options;

    console.log(`Starting enhanced humanization (target: ≤${targetDetection}% AI detection)...`);
    console.log(`Mode: ${useModelBased ? 'LLM-based' : 'Pattern-based'}, Aggressiveness: ${aggressiveness}`);

    // Try Falcon-based humanization first for optimal ≤10% AI detection
    if (useModelBased) {
        try {
            console.log(`Attempting Falcon-enhanced humanization (target: ≤${targetDetection}%)...`);

            // Check if advanced LLM service is available
            if (isAdvancedLLMAvailable()) {
                // Prioritize Falcon models for ≤10% AI detection target
                const preferredModel = targetDetection <= 10 ? 'falcon-3-7b' : 'deepseek-r1';

                const result = await humanizeWithAdvancedLLM(text, {
                    aggressiveness,
                    maintainTone,
                    targetDetection,
                    preferredModel: preferredModel
                });

                if (result.success) {
                    console.log(`Falcon-enhanced humanization successful! Model: ${result.modelName}, Provider: ${result.provider}, Time: ${result.processingTime}ms`);

                    return {
                        text: result.text,
                        method: result.method || 'llm-advanced-falcon',
                        model: result.modelName,
                        provider: result.provider,
                        processingTime: result.processingTime,
                        usage: result.usage,
                        originalLength: text.length,
                        newLength: result.text.length,
                        detectionTarget: targetDetection,
                        success: true
                    };
                } else {
                    console.warn('Falcon-enhanced humanization failed:', result.error);

                    // Try fallback to HuggingFace models with Falcon priority
                    console.log('Trying HuggingFace with Falcon models fallback...');
                    const hfResult = await humanizeTextWithHF(text, {
                        aggressiveness,
                        maintainTone,
                        targetDetection
                    });

                    if (hfResult.success) {
                        console.log(`HuggingFace fallback successful! Model: ${hfResult.model}`);
                        return {
                            text: hfResult.text,
                            method: 'llm-fallback-hf',
                            model: hfResult.model,
                            provider: hfResult.provider,
                            processingTime: hfResult.processingTime,
                            usage: hfResult.usage,
                            originalLength: text.length,
                            newLength: hfResult.text.length,
                            detectionTarget: targetDetection,
                            success: true
                        };
                    }

                    if (!fallbackToPatterns) {
                        throw new Error(`All Falcon-enhanced humanization methods failed: ${result.error}`);
                    }
                }
            } else {
                console.log('No advanced LLM API keys configured, trying HuggingFace with Falcon models...');

                const hfResult = await humanizeTextWithHF(text, {
                    aggressiveness,
                    maintainTone,
                    targetDetection
                });

                if (hfResult.success) {
                    return {
                        text: hfResult.text,
                        method: 'llm-hf-falcon',
                        model: hfResult.model,
                        provider: hfResult.provider,
                        processingTime: hfResult.processingTime,
                        usage: hfResult.usage,
                        originalLength: text.length,
                        newLength: hfResult.text.length,
                        detectionTarget: targetDetection,
                        success: true
                    };
                }

                if (!fallbackToPatterns) {
                    throw new Error('HuggingFace with Falcon models failed and no API keys configured');
                }
            }
        } catch (error) {
            console.error('Falcon-enhanced humanization error:', error.message);

            if (!fallbackToPatterns) {
                throw error;
            }

            console.log('Falling back to pattern-based humanization...');
        }
    }

    // Pattern-based humanization (fallback or primary method)
    return patternBasedHumanization(text, options);
}

/**
 * Pattern-based humanization (legacy system, now used as fallback)
 */
function patternBasedHumanization(text, options = {}) {
    const {
        aggressiveness = 0.7,
        maintainTone = true,
        targetDetection = 10
    } = options;

    const startTime = Date.now();

    // Enhanced AI pattern detection and dynamic aggressiveness adjustment
    const aiAnalysis = comprehensiveAIPatternAnalysis(text);
    const adjustedAggressiveness = dynamicAggressivenessCalculation(text, aggressiveness, aiAnalysis, targetDetection);

    const originalText = text;
    let result = text;

    console.log(`Pattern-based humanization: AI patterns detected: ${aiAnalysis.totalPatterns}, severity: ${aiAnalysis.severity}, adjusted aggressiveness: ${adjustedAggressiveness.toFixed(2)}`);

    // Phase 1: Enhanced Content Analysis and Protection
    const documentAnalysis = analyzeDocumentStructure(result);
    const hesitationFrequency = calculateHesitationFrequency(documentAnalysis);
    const contentTypeAnalysis = analyzeContentType(result);

    // Determine processing approach based on content type and AI detection level
    const hasHighAIDetection = adjustedAggressiveness > 0.85;
    const hasFormalElements = documentAnalysis.formalElements && documentAnalysis.formalElements.length > 0;

    // Apply content-type specific processing strategies
    if (contentTypeAnalysis.processingStrategy === 'conservative_formal') {
        result = processFormattedDocument(originalText, adjustedAggressiveness * 0.8, hesitationFrequency * 0.5, documentAnalysis);
    } else if (contentTypeAnalysis.processingStrategy === 'preserve_technical_terms') {
        result = processTechnicalContent(result, adjustedAggressiveness, contentTypeAnalysis, documentAnalysis);
    } else if (documentAnalysis.preserveFormatting && hasFormalElements && !hasHighAIDetection) {
        result = processFormattedDocument(originalText, adjustedAggressiveness, hesitationFrequency, documentAnalysis);
    } else {
        // Enhanced processing for regular text or high AI detection content
        const protectedSentences = identifyProtectedSentences(result, documentAnalysis);
        const textAnalysis = analyzeTextStructure(result);

        // Phase 2: Enhanced Sentence Restructuring (most critical for ≤10% detection)
        result = enhancedSentenceRestructuring(result, textAnalysis, adjustedAggressiveness, protectedSentences, contentTypeAnalysis);

        // Phase 3: Advanced Context-Aware Synonym Replacement
        result = advancedContextAwareSynonymReplacement(result, adjustedAggressiveness, protectedSentences, contentTypeAnalysis);

        // Phase 4: Enhanced Perplexity and Burstiness
        result = enhancePerplexityAndBurstiness(result, textAnalysis, adjustedAggressiveness, protectedSentences);

        // Phase 5: Sophisticated Human Writing Pattern Injection
        result = sophisticatedHumanPatternInjection(result, adjustedAggressiveness, hesitationFrequency, protectedSentences, contentTypeAnalysis);

        // Phase 6: Advanced Semantic Coherence Disruption
        result = advancedSemanticDisruption(result, adjustedAggressiveness, protectedSentences, aiAnalysis);

        // Phase 7: AI Pattern Mitigation (new phase for ≤10% target)
        result = mitigateSpecificAIPatterns(result, aiAnalysis, adjustedAggressiveness);

        // Preserve paragraph structure for formatted content
        if (documentAnalysis.preserveFormatting) {
            result = preserveParagraphStructure(originalText, result);
        }
    }

    const processingTime = Date.now() - startTime;

    console.log(`Pattern-based humanization completed in ${processingTime}ms`);

    return {
        text: result,
        method: 'pattern-based',
        model: 'internal-patterns',
        provider: 'local',
        processingTime,
        originalLength: originalText.length,
        newLength: result.length,
        aiAnalysis,
        success: true
    };
}

/**
 * Test the humanization system (both LLM and pattern-based)
 */
export async function testHumanizationSystem() {
    const testText = "This is a test sentence to verify the humanization system is working correctly.";

    try {
        // Test LLM-based humanization
        const llmResult = await advancedHumanization(testText, {
            aggressiveness: 0.5,
            useModelBased: true,
            fallbackToPatterns: false
        });

        // Test pattern-based humanization
        const patternResult = await advancedHumanization(testText, {
            aggressiveness: 0.5,
            useModelBased: false
        });

        return {
            success: true,
            llmBased: llmResult,
            patternBased: patternResult,
            message: 'Humanization system test completed successfully'
        };

    } catch (error) {
        return {
            success: false,
            error: error.message,
            message: 'Humanization system test failed'
        };
    }
}

/**
 * Get system configuration and status
 */
export function getHumanizationSystemStatus() {
    const hfConfig = getHFConfiguration();

    return {
        llmBased: {
            available: hfConfig.hasApiToken,
            primaryModel: hfConfig.primaryModel,
            fallbackModel: hfConfig.fallbackModel,
            configuration: hfConfig
        },
        patternBased: {
            available: true,
            method: 'internal-patterns'
        },
        defaultMode: hfConfig.hasApiToken ? 'llm-based' : 'pattern-based'
    };
}

/**
 * Processes formatted documents while preserving structure
 */
function processFormattedDocument(text, aggressiveness, hesitationFrequency, documentAnalysis) {
    const lines = text.split('\n');
    const processedLines = [];

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const trimmedLine = line.trim();

        // Preserve empty lines exactly as they are
        if (!trimmedLine) {
            processedLines.push(line); // This preserves the original whitespace/empty line
            continue;
        }

        // Check if this line should be protected
        if (shouldProtectLine(trimmedLine, documentAnalysis)) {
            processedLines.push(line);
            continue;
        }

        // Process the line content while preserving indentation
        const indentation = line.match(/^\s*/)[0];
        let processedContent = trimmedLine;

        // Apply conservative modifications to non-protected lines
        if (trimmedLine.length > 20) {
            // Apply only safe modifications that don't break structure
            processedContent = applySafeModifications(processedContent, aggressiveness, hesitationFrequency);
        }

        processedLines.push(indentation + processedContent);
    }

    return processedLines.join('\n');
}

/**
 * Checks if a line should be protected from modification
 */
function shouldProtectLine(line, _documentAnalysis) {
    // Protect headings, section numbers, and formal markers
    const protectionPatterns = [
        /^[IVX]+\./,                    // Roman numerals
        /^[A-Z]\./,                     // Letter markers
        /^\d+\./,                       // Number markers
        /^\d+\.\d+/,                    // Decimal numbering
        /^[A-Z][A-Z\s]+:?\s*$/,        // ALL CAPS headings
        /^(Introduction|Conclusion|Summary|Abstract|Background|Methodology|Results):/i,
        /^(Hook|Thesis|Topic|Executive Summary|Key Findings|Strategic Recommendations):/i,
        /^(Step|Phase|Part)\s+\d+/i,   // Step/Phase markers
        /^[-•*]\s/,                     // Bullet points
        /^[a-z]\)\s/,                   // Lettered lists
        /^\d+\)\s/,                     // Numbered lists with parentheses
        /:\s*$/,                        // Lines ending with colon
        /^-\s/                          // Dash lists
    ];

    return protectionPatterns.some(pattern => pattern.test(line));
}

/**
 * Applies safe modifications that don't break document structure
 */
function applySafeModifications(text, aggressiveness, hesitationFrequency) {
    let result = text;

    // Only apply very conservative word replacements
    const safeReplacements = {
        'utilize': 'use',
        'implement': 'put in place',
        'demonstrate': 'show',
        'facilitate': 'help',
        'optimize': 'improve'
    };

    Object.entries(safeReplacements).forEach(([formal, casual]) => {
        if (Math.random() < aggressiveness * 0.3) {
            const regex = new RegExp(`\\b${formal}\\b`, 'gi');
            result = result.replace(regex, casual);
        }
    });

    // Very rarely add hesitation (only for long sentences)
    if (text.length > 50 && Math.random() < hesitationFrequency * 0.5) {
        const hesitations = ['Actually, ', 'Well, '];
        const hesitation = hesitations[Math.floor(Math.random() * hesitations.length)];
        result = hesitation + result.charAt(0).toLowerCase() + result.slice(1);
    }

    return result;
}

/**
 * Analyzes text structure to inform humanization decisions
 */
function analyzeTextStructure(text) {
    const sentences = text.split(/(?<=[.!?])\s+/);
    const words = text.split(/\s+/);
    
    const analysis = {
        sentenceCount: sentences.length,
        averageSentenceLength: sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length,
        wordCount: words.length,
        averageWordLength: words.reduce((sum, w) => sum + w.length, 0) / words.length,
        sentenceLengths: sentences.map(s => s.length),
        complexSentenceRatio: sentences.filter(s => s.includes(',') || s.includes(';')).length / sentences.length,
        formalWordCount: countFormalWords(text),
        aiTriggerCount: countAITriggers(text)
    };
    
    // Calculate burstiness (variation in sentence length)
    const lengths = analysis.sentenceLengths;
    const mean = lengths.reduce((a, b) => a + b, 0) / lengths.length;
    const variance = lengths.reduce((sum, len) => sum + Math.pow(len - mean, 2), 0) / lengths.length;
    analysis.burstiness = Math.sqrt(variance) / mean;
    
    return analysis;
}

/**
 * Advanced sentence restructuring - the most critical component
 */
function advancedSentenceRestructuring(text, _analysis, aggressiveness, protectedSentences = []) {
    let result = text;

    // Check if text has line breaks that should be preserved
    const hasLineBreaks = text.includes('\n');

    if (hasLineBreaks) {
        // Process line by line to preserve formatting
        return processTextWithLineBreaks(text, (sentence) =>
            processSingleSentenceRestructuring(sentence, aggressiveness, protectedSentences)
        );
    }

    const sentences = result.split(/(?<=[.!?])\s+/);

    const restructuredSentences = sentences.map((sentence, index) => {
        // Skip protected sentences (headings, formal content, etc.)
        if (protectedSentences.includes(index)) {
            return sentence;
        }

        // Skip very short sentences
        if (sentence.length < 20) return sentence;

        let newSentence = sentence;

        // 1. Clause reordering (high impact on AI detection)
        if (Math.random() < aggressiveness * 0.4) {
            newSentence = reorderClauses(newSentence);
        }

        // 2. Sentence splitting/combining based on human patterns
        if (Math.random() < aggressiveness * 0.3) {
            if (sentence.length > 80 && sentence.includes(',')) {
                // Split long sentences
                const splitResult = splitSentenceNaturally(newSentence);
                if (splitResult.length > 1) {
                    return splitResult.join(' ');
                }
            }
        }

        // 3. Passive/Active voice variation (humans mix both)
        if (Math.random() < aggressiveness * 0.25) {
            newSentence = varyVoiceNaturally(newSentence);
        }

        // 4. Sentence starter variation
        if (Math.random() < aggressiveness * 0.35) {
            newSentence = varyStarterPhrases(newSentence);
        }

        return newSentence;
    });

    return restructuredSentences.join(' ');
}

/**
 * Reorders clauses within sentences to create more natural flow
 */
function reorderClauses(sentence) {
    // Handle sentences with dependent clauses
    const patterns = [
        // "Because X, Y" -> "Y because X"
        {
            regex: /^(Because|Since|As|When|While|If|Although|Though)\s+([^,]+),\s*(.+)$/i,
            reorder: (match, connector, clause1, clause2) => {
                if (Math.random() > 0.5) {
                    return `${clause2}, ${connector.toLowerCase()} ${clause1}`;
                }
                return match;
            }
        },
        // "X, which Y, Z" -> "X Z, which Y" or other variations
        {
            regex: /^([^,]+),\s*which\s+([^,]+),\s*(.+)$/i,
            reorder: (match, main, which, rest) => {
                const variations = [
                    `${main} ${rest}, which ${which}`,
                    `${main}, and it ${which}, ${rest}`,
                    match // Keep original sometimes
                ];
                return variations[Math.floor(Math.random() * variations.length)];
            }
        }
    ];
    
    for (const pattern of patterns) {
        const match = sentence.match(pattern.regex);
        if (match) {
            return pattern.reorder(...match);
        }
    }
    
    return sentence;
}

/**
 * Splits long sentences naturally at appropriate points
 */
function splitSentenceNaturally(sentence) {
    const splitPoints = [
        { regex: /,\s*(and|but|or|so)\s+/, replacement: '. ' },
        { regex: /;\s*/, replacement: '. ' },
        { regex: /,\s*which\s+/, replacement: '. This ' },
        { regex: /,\s*because\s+/, replacement: '. This is because ' }
    ];
    
    for (const point of splitPoints) {
        if (sentence.match(point.regex)) {
            const parts = sentence.split(point.regex);
            if (parts.length >= 2 && parts[0].length > 30 && parts[1].length > 20) {
                const firstPart = parts[0].trim() + '.';
                const secondPart = parts.slice(1).join(' ').trim();
                const capitalizedSecond = secondPart.charAt(0).toUpperCase() + secondPart.slice(1);
                return [firstPart, capitalizedSecond];
            }
        }
    }
    
    return [sentence];
}

/**
 * Varies voice (active/passive) naturally
 */
function varyVoiceNaturally(sentence) {
    // Simple passive to active conversion
    const passivePattern = /(\w+)\s+(?:is|was|are|were)\s+(\w+ed)\s+by\s+(\w+)/i;
    const passiveMatch = sentence.match(passivePattern);
    
    if (passiveMatch && Math.random() > 0.6) {
        const [full, object, verb, subject] = passiveMatch;
        const activeForm = verb.replace(/ed$/, ''); // Simplified
        return sentence.replace(full, `${subject} ${activeForm} ${object}`);
    }
    
    return sentence;
}

/**
 * Varies sentence starters to break AI patterns
 */
function varyStarterPhrases(sentence) {
    const starters = [
        { pattern: /^However,\s*/i, replacements: ['But ', 'Still, ', 'Yet '] },
        { pattern: /^Therefore,\s*/i, replacements: ['So ', 'Thus, ', 'Hence '] },
        { pattern: /^Furthermore,\s*/i, replacements: ['Also, ', 'Plus, ', 'And '] },
        { pattern: /^Moreover,\s*/i, replacements: ['Also, ', 'Plus, ', 'What\'s more, '] },
        { pattern: /^Additionally,\s*/i, replacements: ['Also, ', 'Plus, ', 'On top of that, '] },
        { pattern: /^In addition,\s*/i, replacements: ['Also, ', 'Plus, ', 'Besides, '] }
    ];
    
    for (const starter of starters) {
        if (sentence.match(starter.pattern)) {
            const replacement = starter.replacements[Math.floor(Math.random() * starter.replacements.length)];
            return sentence.replace(starter.pattern, replacement);
        }
    }
    
    return sentence;
}

/**
 * Context-aware synonym replacement using semantic understanding
 */
function contextAwareSynonymReplacement(text, aggressiveness, protectedSentences = []) {
    let result = text;

    // Check if text has line breaks that should be preserved
    const hasLineBreaks = text.includes('\n');

    if (hasLineBreaks) {
        // Process line by line to preserve formatting
        return processTextWithLineBreaks(text, (sentence) =>
            processSingleSentenceSynonyms(sentence, aggressiveness, protectedSentences)
        );
    }

    const sentences = result.split(/(?<=[.!?])\s+/);
    
    // Advanced synonym groups with context awareness
    const contextualSynonyms = {
        // Verbs - context matters greatly
        'utilize': { 
            formal: ['employ', 'apply'], 
            casual: ['use', 'try'], 
            context: 'tool|method|approach' 
        },
        'demonstrate': { 
            formal: ['illustrate', 'exhibit'], 
            casual: ['show', 'prove'], 
            context: 'example|evidence|proof' 
        },
        'implement': { 
            formal: ['execute', 'deploy'], 
            casual: ['do', 'put in place', 'set up'], 
            context: 'plan|strategy|solution' 
        },
        // Adjectives - tone-sensitive
        'significant': { 
            formal: ['substantial', 'considerable'], 
            casual: ['big', 'major', 'important'], 
            context: 'impact|change|difference' 
        },
        'comprehensive': { 
            formal: ['thorough', 'extensive'], 
            casual: ['complete', 'full', 'detailed'], 
            context: 'analysis|study|review' 
        }
    };
    
    // Process each sentence, skipping protected ones
    const processedSentences = sentences.map((sentence, sentenceIndex) => {
        if (protectedSentences.includes(sentenceIndex)) {
            return sentence;
        }

        // Process each word with context
        const words = sentence.split(/(\s+)/);
        const processedWords = words.map((word, index) => {
            const cleanWord = word.toLowerCase().replace(/[^\w]/g, '');

            if (contextualSynonyms[cleanWord] && Math.random() < aggressiveness * 0.4) {
                const synonymGroup = contextualSynonyms[cleanWord];
                const context = getWordContext(words, index, 3);

                // Choose synonym based on context
                let synonyms;
                if (synonymGroup.context && context.includes(synonymGroup.context)) {
                    synonyms = [...synonymGroup.formal, ...synonymGroup.casual];
                } else if (context.includes('formal') || context.includes('academic')) {
                    synonyms = synonymGroup.formal;
                } else {
                    synonyms = synonymGroup.casual;
                }

                if (synonyms.length > 0) {
                    const chosen = synonyms[Math.floor(Math.random() * synonyms.length)];
                    return preserveCase(word, chosen);
                }
            }

            return word;
        });

        return processedWords.join('');
    });

    return processedSentences.join(' ');
}

/**
 * Gets context around a word for better synonym selection
 */
function getWordContext(words, index, radius) {
    const start = Math.max(0, index - radius);
    const end = Math.min(words.length, index + radius + 1);
    return words.slice(start, end).join(' ').toLowerCase();
}

/**
 * Preserves original word casing when replacing
 */
function preserveCase(original, replacement) {
    if (!original || !replacement) return replacement;
    
    if (original === original.toUpperCase()) {
        return replacement.toUpperCase();
    }
    if (original === original.toLowerCase()) {
        return replacement.toLowerCase();
    }
    if (original[0] === original[0].toUpperCase()) {
        return replacement.charAt(0).toUpperCase() + replacement.slice(1).toLowerCase();
    }
    return replacement;
}

/**
 * Counts formal words that AI tends to overuse
 */
function countFormalWords(text) {
    const formalWords = [
        'utilize', 'implement', 'demonstrate', 'facilitate', 'optimize',
        'comprehensive', 'significant', 'substantial', 'furthermore', 'moreover'
    ];
    
    let count = 0;
    const lowerText = text.toLowerCase();
    formalWords.forEach(word => {
        const regex = new RegExp(`\\b${word}\\b`, 'g');
        const matches = lowerText.match(regex);
        if (matches) count += matches.length;
    });
    
    return count;
}

/**
 * Counts AI trigger phrases
 */
function countAITriggers(text) {
    const triggers = [
        'it is important to note', 'it should be noted', 'in conclusion',
        'to summarize', 'furthermore', 'moreover', 'additionally'
    ];

    let count = 0;
    const lowerText = text.toLowerCase();
    triggers.forEach(trigger => {
        if (lowerText.includes(trigger)) count++;
    });

    return count;
}

/**
 * Enhances perplexity and burstiness - critical for bypassing AI detection
 */
function enhancePerplexityAndBurstiness(text, analysis, aggressiveness, protectedSentences = []) {
    let result = text;
    const sentences = result.split(/(?<=[.!?])\s+/);

    // Calculate target burstiness (humans have higher variation)
    const targetBurstiness = Math.max(0.4, analysis.burstiness || 0.3 * 1.5);

    // Use target burstiness to vary sentence processing
    const useBurstiness = targetBurstiness > 0.5;

    const enhancedSentences = sentences.map((sentence, index) => {
        // Skip protected sentences
        if (protectedSentences.includes(index)) {
            return sentence;
        }

        let newSentence = sentence;

        // 1. Vary sentence complexity dramatically
        // Use burstiness to vary processing intensity
        const processingProbability = useBurstiness ? aggressiveness * 0.6 : aggressiveness * 0.5;
        if (Math.random() < processingProbability) {
            if (sentence.length > 60) {
                // Make some sentences much simpler
                newSentence = simplifyComplexSentence(newSentence);
            } else if (sentence.length < 30) {
                // Make some sentences more complex
                newSentence = complexifySimpleSentence(newSentence);
            }
        }

        // 2. Add unexpected word choices (increase perplexity)
        if (Math.random() < aggressiveness * 0.3) {
            newSentence = addUnexpectedWordChoices(newSentence);
        }

        // 3. Inject human-like redundancy and clarification - TEMPORARILY DISABLED
        // This causes word shifting by inserting parenthetical content
        /*
        if (Math.random() < aggressiveness * 0.2) {
            newSentence = addHumanRedundancy(newSentence);
        }
        */

        return newSentence;
    });

    return enhancedSentences.join(' ');
}

/**
 * Simplifies complex sentences to increase burstiness
 */
function simplifyComplexSentence(sentence) {
    // Remove unnecessary qualifiers
    let simplified = sentence
        .replace(/\b(very|quite|rather|somewhat|fairly|pretty)\s+/gi, '')
        .replace(/\b(in order to|so as to)\b/gi, 'to')
        .replace(/\b(due to the fact that|owing to the fact that)\b/gi, 'because')
        .replace(/\b(in spite of the fact that|despite the fact that)\b/gi, 'although');

    // Break at natural points
    if (simplified.includes(' and ') && simplified.length > 80) {
        const parts = simplified.split(' and ');
        if (parts.length === 2 && parts[0].length > 20 && parts[1].length > 20) {
            return parts[0].trim() + '. ' + parts[1].charAt(0).toUpperCase() + parts[1].slice(1);
        }
    }

    return simplified;
}

/**
 * Adds complexity to simple sentences
 */
function complexifySimpleSentence(sentence) {
    const complexifiers = [
        { pattern: /^(\w+\s+\w+)\s+(\w+)/, replacement: '$1 actually $2' },
        { pattern: /(\w+)\s+(is|are|was|were)\s+(\w+)/, replacement: '$1 $2 really $3' },
        { pattern: /\.$/, replacement: ', which is interesting.' }
    ];

    for (const complexifier of complexifiers) {
        if (sentence.match(complexifier.pattern) && Math.random() > 0.7) {
            return sentence.replace(complexifier.pattern, complexifier.replacement);
        }
    }

    return sentence;
}

/**
 * Adds unexpected word choices to increase perplexity
 */
function addUnexpectedWordChoices(sentence) {
    const unexpectedReplacements = {
        'good': ['solid', 'decent', 'fine', 'okay'],
        'bad': ['rough', 'tough', 'tricky', 'messy'],
        'big': ['huge', 'massive', 'enormous', 'giant'],
        'small': ['tiny', 'little', 'mini', 'compact'],
        'fast': ['quick', 'speedy', 'rapid', 'swift'],
        'slow': ['sluggish', 'gradual', 'steady', 'leisurely']
    };

    let result = sentence;
    Object.entries(unexpectedReplacements).forEach(([common, alternatives]) => {
        const regex = new RegExp(`\\b${common}\\b`, 'gi');
        result = result.replace(regex, (match) => {
            if (Math.random() > 0.7) {
                const alternative = alternatives[Math.floor(Math.random() * alternatives.length)];
                return preserveCase(match, alternative);
            }
            return match;
        });
    });

    return result;
}

/**
 * Adds human-like redundancy and clarification
 */
function addHumanRedundancy(sentence) {
    const redundancyPatterns = [
        { pattern: /\b(important|significant|crucial)\b/gi, addition: ' (really $1)' },
        { pattern: /\b(easy|simple|straightforward)\b/gi, addition: ' (pretty $1)' },
        { pattern: /\b(difficult|hard|challenging)\b/gi, addition: ' (quite $1)' }
    ];

    for (const pattern of redundancyPatterns) {
        if (sentence.match(pattern.pattern) && Math.random() > 0.8) {
            return sentence.replace(pattern.pattern, (match) => {
                return match + pattern.addition.replace('$1', match.toLowerCase());
            });
        }
    }

    return sentence;
}

/**
 * Injects human writing patterns that AI rarely exhibits
 */
function injectHumanWritingPatterns(text, aggressiveness, hesitationFrequency, protectedSentences = []) {
    let result = text;

    // 1. Add human hesitation and self-correction (with controlled frequency)
    result = addHesitationPatterns(result, aggressiveness, hesitationFrequency, protectedSentences);

    // 2. Inject conversational elements (reduced frequency)
    result = addConversationalElements(result, aggressiveness * 0.5, protectedSentences);

    // 3. Add human-like tangents and asides (very reduced frequency)
    result = addHumanTangents(result, aggressiveness * 0.3, protectedSentences);

    // 4. Inject personal perspective markers (reduced frequency)
    result = addPersonalPerspective(result, aggressiveness * 0.4, protectedSentences);

    return result;
}

/**
 * Enhanced hesitation patterns with contextual intelligence
 */
function addHesitationPatterns(text, _aggressiveness, hesitationFrequency, protectedSentences = []) {
    let result = text;
    const sentences = result.split(/(?<=[.!?])\s+/);

    const hesitationMarkers = [
        'Actually,', 'Listen,', 'Well,', 'You know,', 'I mean,',
        'To be honest,', 'Frankly,', 'Look,', 'Honestly,', 'So,'
    ];

    const contextualAlternatives = {
        'transition': ['However,', 'Meanwhile,', 'Furthermore,', 'Additionally,'],
        'emphasis': ['Notably,', 'Importantly,', 'Significantly,', 'Remarkably,'],
        'clarification': ['In other words,', 'That is to say,', 'Specifically,', 'Namely,']
    };

    const enhancedSentences = sentences.map((sentence, index) => {
        // Skip protected sentences (headings, formal content, etc.)
        if (protectedSentences.includes(index)) {
            return sentence;
        }

        // Skip sentences that look like formal content
        if (isInappropriateForHesitation(sentence)) {
            return sentence;
        }

        // Calculate probability based on context
        const probability = calculateHesitationProbability(sentence, hesitationFrequency);

        // Only add hesitation if probability check passes
        if (Math.random() < probability) {
            return addContextualHesitation(sentence, contextualAlternatives, hesitationMarkers);
        }

        // Apply other humanization techniques without hesitation markers
        return applySubtleHumanization(sentence);
    });

    return enhancedSentences.join(' ');
}

/**
 * Apply subtle humanization techniques without hesitation markers
 */
function applySubtleHumanization(sentence) {
    let processed = sentence;

    // Subtle word variations
    processed = applyWordVariations(processed);

    // Slight structural adjustments
    processed = applyStructuralVariations(processed);

    return processed;
}

/**
 * Process text while preserving line breaks
 */
function processTextWithLineBreaks(text, processingFunction) {
    const lines = text.split('\n');

    return lines.map(line => {
        const trimmedLine = line.trim();

        // Skip empty lines or lines that should be protected
        if (!trimmedLine || isInappropriateForHesitation(trimmedLine)) {
            return line;
        }

        // Process the line content while preserving indentation
        const indentation = line.match(/^\s*/)[0];
        const processedContent = processingFunction(trimmedLine);

        return indentation + processedContent;
    }).join('\n');
}

/**
 * Process a single sentence for restructuring
 */
function processSingleSentenceRestructuring(sentence, aggressiveness, _protectedSentences) {
    if (!sentence.trim() || isInappropriateForHesitation(sentence)) {
        return sentence;
    }

    let newSentence = sentence;

    // Apply restructuring techniques
    if (Math.random() < aggressiveness * 0.4) {
        // 1. Clause reordering
        newSentence = reorderClauses(newSentence);
    }

    if (Math.random() < aggressiveness * 0.3) {
        if (sentence.length > 80 && sentence.includes(',')) {
            // Split long sentences
            const splitResult = splitSentenceNaturally(newSentence);
            if (splitResult.length > 1) {
                return splitResult.join(' ');
            }
        }
    }

    return newSentence;
}

/**
 * Process a single sentence for synonym replacement
 */
function processSingleSentenceSynonyms(sentence, aggressiveness, _protectedSentences) {
    if (!sentence.trim() || isInappropriateForHesitation(sentence)) {
        return sentence;
    }

    // Apply basic synonym replacement logic here
    // This is a simplified version - you can expand this with the full synonym logic
    let result = sentence;

    // Basic word variations
    const variations = {
        'very': ['quite', 'rather', 'pretty', 'fairly'],
        'also': ['additionally', 'furthermore', 'moreover', 'as well'],
        'but': ['however', 'yet', 'although', 'though'],
        'because': ['since', 'as', 'due to the fact that'],
        'shows': ['demonstrates', 'indicates', 'reveals', 'suggests']
    };

    for (const [original, alternatives] of Object.entries(variations)) {
        if (Math.random() < aggressiveness * 0.3) {
            const regex = new RegExp(`\\b${original}\\b`, 'gi');
            const replacement = alternatives[Math.floor(Math.random() * alternatives.length)];
            result = result.replace(regex, replacement);
        }
    }

    return result;
}

/**
 * Enhanced word variations with aggressive AI-detection bypass patterns
 */
function applyWordVariations(sentence) {
    // Comprehensive AI-typical word replacements
    const aggressiveVariations = {
        // AI loves these formal words - replace aggressively
        'artificial intelligence': ['AI technology', 'machine learning systems', 'smart technology', 'automated systems'],
        'represents': ['is', 'acts as', 'serves as', 'functions as'],
        'transformative': ['game-changing', 'revolutionary', 'groundbreaking', 'innovative'],
        'fundamentally': ['completely', 'totally', 'entirely', 'thoroughly'],
        'revolutionizes': ['changes', 'transforms', 'reshapes', 'modernizes'],
        'organizations': ['companies', 'businesses', 'firms', 'enterprises'],
        'increasingly': ['more and more', 'progressively', 'gradually', 'steadily'],
        'implementing': ['using', 'adopting', 'deploying', 'putting in place'],
        'optimize': ['improve', 'enhance', 'boost', 'maximize'],
        'operational efficiency': ['performance', 'productivity', 'effectiveness', 'workflow'],
        'enhance': ['improve', 'boost', 'strengthen', 'upgrade'],
        'competitive advantages': ['edge over competitors', 'market benefits', 'business benefits', 'strategic benefits'],
        'comprehensive': ['complete', 'thorough', 'full', 'extensive'],
        'strategic planning': ['planning', 'strategy development', 'business planning', 'preparation'],
        'substantial': ['significant', 'considerable', 'major', 'large'],
        'financial investment': ['investment', 'funding', 'capital', 'money'],
        'numerous studies': ['many studies', 'research', 'multiple reports', 'various analyses'],
        'demonstrate': ['show', 'prove', 'indicate', 'reveal'],
        'utilizing': ['using', 'employing', 'applying', 'working with'],
        'experience': ['see', 'achieve', 'get', 'gain'],
        'significant improvements': ['better results', 'major gains', 'notable progress', 'substantial progress'],
        'productivity metrics': ['performance measures', 'efficiency indicators', 'output measures', 'results'],
        'performance indicators': ['metrics', 'measurements', 'benchmarks', 'results'],
        'furthermore': ['also', 'additionally', 'moreover', 'plus'],
        'facilitates': ['enables', 'helps', 'supports', 'allows'],
        'decision-making processes': ['decision making', 'choices', 'business decisions', 'strategic choices'],
        'advanced': ['sophisticated', 'cutting-edge', 'modern', 'state-of-the-art'],
        'data analytics': ['data analysis', 'information processing', 'data insights', 'analytics'],
        'predictive modeling': ['forecasting', 'prediction tools', 'modeling', 'trend analysis'],
        'capabilities': ['abilities', 'features', 'functions', 'tools'],
        'sophisticated systems': ['complex systems', 'advanced tools', 'smart systems', 'modern platforms'],
        'enable': ['allow', 'help', 'let', 'make possible'],
        'leverage': ['use', 'utilize', 'take advantage of', 'harness'],
        'vast amounts': ['large amounts', 'huge volumes', 'massive quantities', 'tons'],
        'generate': ['create', 'produce', 'develop', 'make'],
        'actionable insights': ['useful information', 'practical findings', 'valuable data', 'key insights'],
        'business outcomes': ['results', 'business results', 'performance', 'success'],

        // Common AI words
        'very': ['quite', 'rather', 'pretty', 'fairly', 'really'],
        'also': ['additionally', 'furthermore', 'moreover', 'as well', 'too'],
        'but': ['however', 'yet', 'although', 'though', 'still'],
        'because': ['since', 'as', 'due to the fact that', 'given that'],
        'shows': ['demonstrates', 'indicates', 'reveals', 'suggests', 'proves'],
        'important': ['significant', 'crucial', 'vital', 'essential', 'key'],
        'different': ['various', 'distinct', 'diverse', 'alternative', 'separate'],
        'good': ['effective', 'beneficial', 'valuable', 'positive', 'excellent'],
        'bad': ['negative', 'detrimental', 'harmful', 'problematic', 'poor'],
        'big': ['large', 'substantial', 'significant', 'considerable', 'major'],
        'small': ['minor', 'limited', 'modest', 'minimal', 'tiny']
    };

    let result = sentence;

    // Apply aggressive replacements with higher probability for AI-typical phrases
    for (const [original, alternatives] of Object.entries(aggressiveVariations)) {
        // Higher replacement probability for longer, more AI-typical phrases
        const replacementProbability = original.includes(' ') ? 0.8 : 0.6;

        if (Math.random() < replacementProbability) {
            const regex = new RegExp(`\\b${original.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'gi');
            const replacement = alternatives[Math.floor(Math.random() * alternatives.length)];
            result = result.replace(regex, replacement);
        }
    }

    return result;
}

/**
 * Aggressive structural changes for AI detection bypass
 */
function applyStructuralVariations(sentence) {
    let result = sentence;

    // Aggressive sentence pattern restructuring

    // 1. Convert passive to active voice and vice versa
    if (Math.random() < 0.4) {
        // "AI is used by companies" → "Companies use AI"
        result = result.replace(/(\w+)\s+is\s+(\w+ed)\s+by\s+(\w+)/gi, '$3 $2 $1');

        // "Companies implement AI" → "AI is implemented by companies" (sometimes reverse)
        if (Math.random() < 0.3) {
            result = result.replace(/(\w+)\s+(implement|use|deploy|adopt)\s+(\w+)/gi, '$3 is $2ed by $1');
        }
    }

    // 2. Restructure "X enables Y" patterns
    if (Math.random() < 0.5) {
        result = result.replace(/(\w+)\s+enables?\s+(\w+)\s+to\s+(\w+)/gi, (_match, subject, object, verb) => {
            const alternatives = [
                `${object} can ${verb} through ${subject}`,
                `With ${subject}, ${object} can ${verb}`,
                `${subject} allows ${object} to ${verb}`,
                `${object} ${verb} using ${subject}`
            ];
            return alternatives[Math.floor(Math.random() * alternatives.length)];
        });
    }

    // 3. Break down complex sentences with multiple clauses
    if (Math.random() < 0.3 && sentence.includes(',') && sentence.length > 80) {
        // Split at logical break points
        result = result.replace(/,\s+(which|that|where|when)\s+/gi, '. This ');
        result = result.replace(/,\s+(however|furthermore|moreover|additionally)\s+/gi, '. $1, ');
    }

    // 4. Restructure "through X" patterns
    if (Math.random() < 0.4) {
        result = result.replace(/through\s+(\w+(?:\s+\w+)*)/gi, (_match, method) => {
            const alternatives = [
                `using ${method}`,
                `via ${method}`,
                `by means of ${method}`,
                `with ${method}`
            ];
            return alternatives[Math.floor(Math.random() * alternatives.length)];
        });
    }

    // 5. Vary sentence starters
    if (Math.random() < 0.3) {
        // "Organizations can..." → "Companies are able to..."
        result = result.replace(/^Organizations\s+can\s+/i, () => {
            const alternatives = ['Companies are able to ', 'Businesses can ', 'Firms have the ability to '];
            return alternatives[Math.floor(Math.random() * alternatives.length)];
        });

        // "The implementation of..." → "Implementing..."
        result = result.replace(/^The\s+implementation\s+of\s+/i, 'Implementing ');

        // "Furthermore," → various alternatives
        result = result.replace(/^Furthermore,\s*/i, () => {
            const alternatives = ['Also, ', 'Additionally, ', 'Moreover, ', 'Plus, ', 'On top of that, '];
            return alternatives[Math.floor(Math.random() * alternatives.length)];
        });
    }

    // 6. Add natural human hesitation/thinking patterns
    if (Math.random() < 0.2 && sentence.length > 50) {
        const hesitations = [
            ', you know,',
            ', I mean,',
            ', basically,',
            ', essentially,',
            ', in other words,'
        ];
        const hesitation = hesitations[Math.floor(Math.random() * hesitations.length)];

        // Insert at natural break points
        if (sentence.includes(',')) {
            result = result.replace(/,\s/, hesitation + ' ');
        }
    }

    return result;
}

/**
 * Calculate hesitation probability based on sentence context
 */
function calculateHesitationProbability(sentence, baseFrequency) {
    let probability = baseFrequency;

    // Reduce probability for certain contexts
    if (sentence.length < 10) probability *= 0.2;          // Short sentences
    if (isQuestionOrExclamation(sentence)) probability *= 0.3;
    if (containsNumbers(sentence)) probability *= 0.4;
    if (isFormalTone(sentence)) probability *= 0.5;
    if (isListItem(sentence)) probability = 0;        // Never add to lists

    // Increase probability for longer, complex sentences
    if (sentence.length > 50) probability *= 1.2;
    if (hasComplexStructure(sentence)) probability *= 1.1;

    return Math.min(probability, 0.05); // Cap at 5%
}

/**
 * Add contextual hesitation based on sentence type
 */
function addContextualHesitation(sentence, contextualAlternatives, hesitationMarkers) {
    // Choose appropriate hesitation based on context
    let hesitationType = 'transition';

    if (isEmphatic(sentence)) hesitationType = 'emphasis';
    if (needsClarification(sentence)) hesitationType = 'clarification';

    const markers = contextualAlternatives[hesitationType] || hesitationMarkers;
    const marker = markers[Math.floor(Math.random() * markers.length)];

    return `${marker} ${sentence.charAt(0).toLowerCase()}${sentence.slice(1)}`;
}

/**
 * Helper functions for sentence analysis
 */
function isQuestionOrExclamation(sentence) {
    return /[?!]/.test(sentence);
}

function containsNumbers(sentence) {
    return /\d+/.test(sentence);
}

function isFormalTone(sentence) {
    const formalIndicators = [
        /\btherefore\b/i, /\bfurthermore\b/i, /\bmoreover\b/i,
        /\bconsequently\b/i, /\bnevertheless\b/i, /\bhowever\b/i,
        /\bin conclusion\b/i, /\bin summary\b/i
    ];

    return formalIndicators.some(pattern => pattern.test(sentence));
}

function isListItem(sentence) {
    return /^\s*[-•*]/.test(sentence) || /^\s*\d+\./.test(sentence);
}

function hasComplexStructure(sentence) {
    const commaCount = (sentence.match(/,/g) || []).length;
    const clauseMarkers = (sentence.match(/\b(which|that|where|when|while|although|because|since|if|unless)\b/gi) || []).length;

    return commaCount >= 2 || clauseMarkers >= 1;
}

function isEmphatic(sentence) {
    const emphaticPatterns = [
        /\b(very|extremely|incredibly|absolutely|definitely|certainly)\b/i,
        /\b(crucial|essential|vital|important|significant)\b/i,
        /!/
    ];

    return emphaticPatterns.some(pattern => pattern.test(sentence));
}

function needsClarification(sentence) {
    const clarificationPatterns = [
        /\b(means|refers to|indicates|suggests|implies)\b/i,
        /\b(in other words|that is|namely|specifically)\b/i
    ];

    return clarificationPatterns.some(pattern => pattern.test(sentence));
}

/**
 * Enhanced protection patterns for inappropriate hesitation placement
 */
function isInappropriateForHesitation(sentence) {
    const trimmed = sentence.trim();

    // Protect very short sentences (likely headings or labels)
    if (trimmed.length < 10) return true;

    // Enhanced protection patterns
    const protectedPatterns = [
        /^[IVX]+\.\s/,                    // Roman numerals (I., II., III.)
        /^[A-Z]\.\s/,                     // Letter headings (A., B., C.)
        /^\d+\.\s/,                       // Number headings (1., 2., 3.)
        /^#+\s/,                          // Markdown headers
        /^[A-Z][^:]*:/,                   // Title-like patterns
        /^\s*[-•*]\s/,                    // Bullet points
        /^\s*\([a-z]\)/,                  // Lettered lists (a), (b), (c)
        /^\s*\(\d+\)/,                    // Numbered lists (1), (2), (3)
        /^[A-Z\s]+:$/,                    // ALL CAPS headings
        /^(Introduction|Conclusion|Summary|Abstract|Overview):/i,
        /^(Hook|Thesis|Topic|Executive Summary|Key Findings):/i,
        /^(Step|Phase|Part)\s+\d+/i,     // Step/Phase markers
        /:\s*$/,                          // Lines ending with colon
        /^-\s/                            // Dash lists
    ];

    // Check against protected patterns
    for (const pattern of protectedPatterns) {
        if (pattern.test(trimmed)) {
            return true;
        }
    }

    // Additional checks for common document structures
    if (trimmed.length === 0) return true; // Empty lines
    if (trimmed.length < 3) return true;   // Very short lines
    if (isHeading(trimmed)) return true;
    if (isTechnicalTerm(trimmed)) return true;

    return false;
}

/**
 * Check if line looks like a heading
 */
function isHeading(line) {
    const headingIndicators = [
        /^[A-Z][^.!?]*$/,                 // All caps or title case, no punctuation
        /^\d+\.\d+/,                      // Section numbers like 1.1, 2.3
        /^Chapter \d+/i,                  // Chapter headings
        /^Section [A-Z]/i,                // Section headings
        /^Part [IVX]+/i,                  // Part headings
        /^[A-Z\s]+$/                      // All caps
    ];

    return headingIndicators.some(pattern => pattern.test(line.trim()));
}

/**
 * Check if line contains technical terms or proper nouns
 */
function isTechnicalTerm(line) {
    const technicalPatterns = [
        /\b[A-Z]{2,}\b/,                  // Acronyms
        /\b\d+(\.\d+)*\b/,                // Version numbers
        /\b[A-Z][a-z]+[A-Z][a-z]*\b/,    // CamelCase
        /\b\w+\.\w+\b/,                   // Dotted notation
        /\b(API|URL|HTTP|HTML|CSS|JS|JSON|XML|SQL)\b/i
    ];

    return technicalPatterns.some(pattern => pattern.test(line));
}

/**
 * Detect high AI patterns and adjust aggressiveness accordingly
 */
function detectAndAdjustAggressiveness(text, baseAggressiveness) {
    let aiScore = 0;
    const aiPatterns = [
        // Formal AI language patterns
        /artificial intelligence/gi,
        /transformative technology/gi,
        /fundamentally revolutionizes/gi,
        /operational efficiency/gi,
        /competitive advantages/gi,
        /comprehensive strategic planning/gi,
        /substantial financial investment/gi,
        /numerous studies demonstrate/gi,
        /significant improvements/gi,
        /productivity metrics/gi,
        /performance indicators/gi,
        /facilitates enhanced/gi,
        /decision-making processes/gi,
        /advanced data analytics/gi,
        /predictive modeling capabilities/gi,
        /sophisticated systems/gi,
        /actionable insights/gi,
        /business outcomes/gi,

        // AI sentence patterns
        /\w+ represents a \w+ that/gi,
        /organizations across various industries/gi,
        /increasingly implementing/gi,
        /requires comprehensive/gi,
        /however, numerous/gi,
        /furthermore, \w+ facilitates/gi,
        /these sophisticated/gi,
        /enable organizations to/gi,
        /leverage vast amounts/gi,

        // Repetitive formal structures
        /\w+ systems? \w+ \w+ to \w+/gi,
        /the implementation of \w+ requires/gi,
        /companies utilizing \w+ experience/gi
    ];

    // Count AI pattern matches
    aiPatterns.forEach(pattern => {
        const matches = text.match(pattern);
        if (matches) {
            aiScore += matches.length;
        }
    });

    // Calculate AI density
    const wordCount = text.split(/\s+/).length;
    const aiDensity = aiScore / wordCount;

    // Adjust aggressiveness based on AI density
    let adjustedAggressiveness = baseAggressiveness;

    if (aiDensity > 0.1) { // High AI density
        adjustedAggressiveness = Math.min(0.95, baseAggressiveness + 0.3);
    } else if (aiDensity > 0.05) { // Medium AI density
        adjustedAggressiveness = Math.min(0.9, baseAggressiveness + 0.2);
    } else if (aiDensity > 0.02) { // Low AI density
        adjustedAggressiveness = Math.min(0.85, baseAggressiveness + 0.1);
    }

    console.log(`AI Detection: ${aiScore} patterns, density: ${(aiDensity * 100).toFixed(2)}%, aggressiveness: ${baseAggressiveness} → ${adjustedAggressiveness.toFixed(2)}`);

    return adjustedAggressiveness;
}

/**
 * Comprehensive AI pattern analysis for enhanced detection
 */
function comprehensiveAIPatternAnalysis(text) {
    // Input validation
    if (!text || typeof text !== 'string') {
        console.warn('Invalid input to comprehensiveAIPatternAnalysis:', typeof text);
        return {
            totalPatterns: 0,
            severity: 'low',
            patternTypes: {},
            recommendations: []
        };
    }

    const analysis = {
        totalPatterns: 0,
        severity: 'low',
        patternTypes: {},
        recommendations: []
    };

    // Enhanced AI pattern categories with more sophisticated detection
    const patternCategories = {
        formalTransitions: {
            patterns: [
                /\b(furthermore|moreover|additionally|consequently|therefore|thus|hence|accordingly|subsequently|nevertheless|nonetheless|however|meanwhile|simultaneously|ultimately|essentially|fundamentally)\b/gi,
                /\b(in conclusion|to summarize|in summary|to conclude|finally|lastly|in the final analysis|ultimately|all things considered)\b/gi
            ],
            weight: 2.0,
            description: 'Overuse of formal transitional phrases'
        },
        repetitiveStarters: {
            patterns: [
                /^(It is important to note that|It should be noted that|It is worth mentioning that|It is essential to understand that|It is crucial to recognize that)\s/gmi,
                /^(The|This|These|Those)\s+(?:fact|reality|truth|issue|problem|challenge|opportunity|benefit|advantage|disadvantage|aspect|element|component|factor|consideration)\s/gmi
            ],
            weight: 1.8,
            description: 'Repetitive sentence starters'
        },
        passiveVoiceOveruse: {
            patterns: [
                /\b(is|are|was|were|been|being)\s+(being\s+)?(analyzed|examined|studied|investigated|explored|considered|evaluated|assessed|reviewed|discussed|addressed|handled|managed|processed|implemented|executed|performed|conducted)\b/gi,
                /\b(can be|will be|should be|must be|may be|might be)\s+(achieved|accomplished|attained|obtained|acquired|gained|secured|established|created|developed|designed|constructed|built)\b/gi
            ],
            weight: 1.5,
            description: 'Excessive passive voice usage'
        },
        intensifierOverload: {
            patterns: [
                /\b(extremely|incredibly|remarkably|exceptionally|extraordinarily|tremendously|enormously|immensely|vastly|hugely|massively|significantly|substantially|considerably)\b/gi,
                /\b(absolutely|completely|entirely|fully|totally|wholly|utterly|thoroughly|comprehensively|extensively|perfectly|ideally|optimally)\b/gi
            ],
            weight: 1.3,
            description: 'Overuse of intensifiers and superlatives'
        },
        hedgingClusters: {
            patterns: [
                /\b(might|could|would|should|may|can|possibly|probably|likely|perhaps|maybe|potentially)\b.*?\b(might|could|would|should|may|can|possibly|probably|likely|perhaps|maybe|potentially)\b/gi,
                /\b(seems?|appears?|suggests?|indicates?|implies?|tends?)\s+to\b.*?\b(seems?|appears?|suggests?|indicates?|implies?|tends?)\s+to\b/gi
            ],
            weight: 1.4,
            description: 'Clustered hedging language'
        },
        structuralPredictability: {
            patterns: [
                /^.{50,80}\.\s+.{50,80}\.\s+.{50,80}\./gm, // Similar sentence lengths
                /\b(first|second|third|fourth|fifth|finally)\b.*?\b(first|second|third|fourth|fifth|finally)\b/gi // Predictable enumeration
            ],
            weight: 1.6,
            description: 'Predictable structural patterns'
        },
        vocabularyFormality: {
            patterns: [
                /\b(utilize|implement|demonstrate|facilitate|optimize|enhance|leverage|establish|maintain|ensure|provide|develop|create|generate|produce|achieve|accomplish|attain|obtain|acquire|secure)\b/gi,
                /\b(comprehensive|substantial|significant|considerable|extensive|thorough|detailed|systematic|methodical|strategic|effective|efficient|successful|optimal|ideal|perfect)\b/gi
            ],
            weight: 1.2,
            description: 'Overuse of formal vocabulary'
        }
    };

    // Analyze each pattern category
    Object.entries(patternCategories).forEach(([category, config]) => {
        let categoryMatches = 0;
        config.patterns.forEach(pattern => {
            // Ensure text is valid before calling match
            if (text && typeof text === 'string') {
                try {
                    const matches = text.match(pattern);
                    if (matches) {
                        categoryMatches += matches.length;
                    }
                } catch (error) {
                    console.warn('Pattern matching error:', error.message, 'Pattern:', pattern);
                }
            }
        });

        if (categoryMatches > 0) {
            analysis.patternTypes[category] = {
                count: categoryMatches,
                weight: config.weight,
                score: categoryMatches * config.weight,
                description: config.description
            };
            analysis.totalPatterns += categoryMatches * config.weight;
        }
    });

    // Calculate severity and recommendations
    const wordCount = text.split(/\s+/).length;
    const patternDensity = (analysis.totalPatterns / wordCount) * 100;

    if (patternDensity > 30) {
        analysis.severity = 'critical';
        analysis.recommendations.push('Requires maximum aggressiveness transformation');
    } else if (patternDensity > 20) {
        analysis.severity = 'high';
        analysis.recommendations.push('Requires aggressive transformation');
    } else if (patternDensity > 10) {
        analysis.severity = 'medium';
        analysis.recommendations.push('Requires moderate transformation');
    } else {
        analysis.severity = 'low';
        analysis.recommendations.push('Requires light transformation');
    }

    return analysis;
}

/**
 * Dynamic aggressiveness calculation based on AI analysis and target detection
 */
function dynamicAggressivenessCalculation(text, baseAggressiveness, aiAnalysis, targetDetection) {
    // Input validation
    if (!text || typeof text !== 'string') {
        console.warn('Invalid input to dynamicAggressivenessCalculation:', typeof text);
        return baseAggressiveness; // Return base value if text is invalid
    }

    const wordCount = text.split(/\s+/).length;
    const patternDensity = (aiAnalysis.totalPatterns / wordCount) * 100;

    // Base adjustment based on pattern density
    let adjustment = 0;

    // More aggressive scaling for lower target detection rates
    const targetMultiplier = targetDetection <= 5 ? 3.0 : targetDetection <= 10 ? 2.5 : targetDetection <= 20 ? 1.8 : 1.0;

    if (patternDensity > 30) {
        adjustment = 0.30 * targetMultiplier; // Critical: maximum boost
    } else if (patternDensity > 20) {
        adjustment = 0.25 * targetMultiplier; // High: major boost
    } else if (patternDensity > 10) {
        adjustment = 0.20 * targetMultiplier; // Medium: significant boost
    } else if (patternDensity > 5) {
        adjustment = 0.15 * targetMultiplier; // Low: moderate boost
    }

    // Additional adjustments based on specific pattern types
    if (aiAnalysis.patternTypes.formalTransitions && aiAnalysis.patternTypes.formalTransitions.count > 5) {
        adjustment += 0.05 * targetMultiplier;
    }
    if (aiAnalysis.patternTypes.passiveVoiceOveruse && aiAnalysis.patternTypes.passiveVoiceOveruse.count > 8) {
        adjustment += 0.05 * targetMultiplier;
    }
    if (aiAnalysis.patternTypes.structuralPredictability && aiAnalysis.patternTypes.structuralPredictability.count > 3) {
        adjustment += 0.08 * targetMultiplier;
    }

    const finalAggressiveness = Math.min(0.98, baseAggressiveness + adjustment);

    return finalAggressiveness;
}

/**
 * Analyze content type for specialized processing
 */
function analyzeContentType(text) {
    const analysis = {
        type: 'general',
        characteristics: [],
        processingStrategy: 'standard'
    };

    // Detect formal documents
    if (text.match(/\b(executive summary|introduction|methodology|conclusion|references|bibliography)\b/gi)) {
        analysis.type = 'formal_document';
        analysis.characteristics.push('structured_sections');
        analysis.processingStrategy = 'conservative_formal';
    }

    // Detect technical content
    if (text.match(/\b(API|algorithm|database|framework|implementation|configuration|deployment)\b/gi)) {
        analysis.type = 'technical';
        analysis.characteristics.push('technical_terminology');
        analysis.processingStrategy = 'preserve_technical_terms';
    }

    // Detect academic writing
    if (text.match(/\b(research|study|analysis|findings|hypothesis|methodology|literature review)\b/gi)) {
        analysis.type = 'academic';
        analysis.characteristics.push('research_oriented');
        analysis.processingStrategy = 'academic_style_preservation';
    }

    // Detect business content
    if (text.match(/\b(strategy|revenue|market|customer|business|organization|management|operations)\b/gi)) {
        analysis.type = 'business';
        analysis.characteristics.push('business_terminology');
        analysis.processingStrategy = 'professional_tone_maintenance';
    }

    return analysis;
}

/**
 * Enhanced sentence restructuring with advanced techniques for ≤10% AI detection
 */
function enhancedSentenceRestructuring(text, textAnalysis, aggressiveness, protectedSentences = [], contentTypeAnalysis = {}) {
    let result = text;

    // Check if text has line breaks that should be preserved
    const hasLineBreaks = text.includes('\n');

    if (hasLineBreaks) {
        // Process line by line to preserve formatting
        return processTextWithLineBreaks(text, (sentence) =>
            processAdvancedSentenceRestructuring(sentence, aggressiveness, protectedSentences, contentTypeAnalysis)
        );
    }

    const sentences = result.split(/(?<=[.!?])\s+/);
    const processedSentences = sentences.map(sentence => {
        if (protectedSentences.includes(sentence.trim())) {
            return sentence;
        }
        return processAdvancedSentenceRestructuring(sentence, aggressiveness, protectedSentences, contentTypeAnalysis);
    });

    return processedSentences.join(' ');
}

/**
 * Process individual sentence with advanced restructuring techniques
 */
function processAdvancedSentenceRestructuring(sentence, aggressiveness, protectedSentences, contentTypeAnalysis) {
    if (!sentence.trim() || isInappropriateForHesitation(sentence)) {
        return sentence;
    }

    let newSentence = sentence;

    // 1. Advanced clause reordering with higher probability for high aggressiveness
    if (Math.random() < Math.min(0.9, aggressiveness * 0.8)) {
        newSentence = advancedClauseReordering(newSentence);
    }

    // 2. Passive to active voice conversion with increased probability
    if (Math.random() < Math.min(0.8, aggressiveness * 0.7)) {
        newSentence = convertPassiveToActive(newSentence);
    }

    // 3. Complex sentence splitting with natural flow - TEMPORARILY DISABLED
    // This is causing word shifting issues by splitting sentences and then
    // not properly handling the rejoining process
    /*
    if (Math.random() < Math.min(0.7, aggressiveness * 0.6)) {
        if (sentence.length > 60 && sentence.includes(',')) { // Reduced threshold
            const splitResult = intelligentSentenceSplitting(newSentence);
            if (splitResult.length > 1) {
                return splitResult.join(' ');
            }
        }
    }
    */

    // 4. Sentence combining for variety
    if (Math.random() < Math.min(0.6, aggressiveness * 0.5)) {
        newSentence = addSentenceVariety(newSentence);
    }

    // 5. Syntactic pattern disruption with higher probability
    if (Math.random() < Math.min(0.9, aggressiveness * 0.8)) {
        newSentence = disruptSyntacticPatterns(newSentence, contentTypeAnalysis);
    }

    return newSentence;
}

/**
 * Advanced clause reordering with sophisticated patterns
 */
function advancedClauseReordering(sentence) {
    // Handle complex sentences with multiple clauses
    const clausePatterns = [
        {
            // "Although X, Y" -> "Y, although X" or "Y. This is despite X"
            pattern: /^Although\s+([^,]+),\s*(.+)$/i,
            replacements: [
                (match, clause1, clause2) => `${clause2}, although ${clause1.toLowerCase()}`,
                (match, clause1, clause2) => `${clause2}. This is despite ${clause1.toLowerCase()}`
            ]
        },
        {
            // "Because X, Y" -> "Y because X" or "Y. The reason is X"
            pattern: /^Because\s+([^,]+),\s*(.+)$/i,
            replacements: [
                (match, clause1, clause2) => `${clause2} because ${clause1.toLowerCase()}`,
                (match, clause1, clause2) => `${clause2}. The reason is ${clause1.toLowerCase()}`
            ]
        },
        {
            // "When X, Y" -> "Y when X" or "Y. This happens when X"
            pattern: /^When\s+([^,]+),\s*(.+)$/i,
            replacements: [
                (match, clause1, clause2) => `${clause2} when ${clause1.toLowerCase()}`,
                (match, clause1, clause2) => `${clause2}. This happens when ${clause1.toLowerCase()}`
            ]
        },
        {
            // "If X, then Y" -> "Y if X" or "Y, provided that X"
            pattern: /^If\s+([^,]+),\s*(?:then\s+)?(.+)$/i,
            replacements: [
                (match, clause1, clause2) => `${clause2} if ${clause1.toLowerCase()}`,
                (match, clause1, clause2) => `${clause2}, provided that ${clause1.toLowerCase()}`
            ]
        }
    ];

    for (const pattern of clausePatterns) {
        const match = sentence.match(pattern.pattern);
        if (match) {
            const replacement = pattern.replacements[Math.floor(Math.random() * pattern.replacements.length)];
            return replacement(...match);
        }
    }

    return sentence;
}

/**
 * Convert passive voice to active voice
 */
function convertPassiveToActive(sentence) {
    const passivePatterns = [
        {
            // "X is analyzed by Y" -> "Y analyzes X"
            pattern: /(.+?)\s+(?:is|are|was|were)\s+(analyzed|examined|studied|investigated|reviewed|discussed|handled|managed|processed|implemented|executed|performed|conducted)\s+by\s+(.+)/i,
            replacement: (match, subject, verb, agent) => {
                const activeVerb = getActiveForm(verb);
                return `${agent} ${activeVerb} ${subject}`;
            }
        },
        {
            // "X can be achieved" -> "We can achieve X" or "You can achieve X"
            pattern: /(.+?)\s+(?:can|will|should|must|may|might)\s+be\s+(achieved|accomplished|attained|obtained|acquired|gained|secured|established|created|developed|designed|constructed|built)/i,
            replacement: (match, subject, verb) => {
                const activeVerb = getActiveForm(verb);
                const agents = ['we', 'you', 'organizations', 'companies', 'teams'];
                const agent = agents[Math.floor(Math.random() * agents.length)];
                return `${agent} can ${activeVerb} ${subject}`;
            }
        }
    ];

    for (const pattern of passivePatterns) {
        const match = sentence.match(pattern.pattern);
        if (match) {
            return pattern.replacement(...match);
        }
    }

    return sentence;
}

/**
 * Get active form of verb
 */
function getActiveForm(verb) {
    const verbMap = {
        'analyzed': 'analyze',
        'examined': 'examine',
        'studied': 'study',
        'investigated': 'investigate',
        'reviewed': 'review',
        'discussed': 'discuss',
        'handled': 'handle',
        'managed': 'manage',
        'processed': 'process',
        'implemented': 'implement',
        'executed': 'execute',
        'performed': 'perform',
        'conducted': 'conduct',
        'achieved': 'achieve',
        'accomplished': 'accomplish',
        'attained': 'attain',
        'obtained': 'obtain',
        'acquired': 'acquire',
        'gained': 'gain',
        'secured': 'secure',
        'established': 'establish',
        'created': 'create',
        'developed': 'develop',
        'designed': 'design',
        'constructed': 'construct',
        'built': 'build'
    };

    return verbMap[verb.toLowerCase()] || verb;
}

/**
 * Intelligent sentence splitting with natural flow
 */
function intelligentSentenceSplitting(sentence) {
    // Look for natural break points
    const breakPoints = [
        { pattern: /,\s+(however|nevertheless|nonetheless|furthermore|moreover|additionally|consequently|therefore|thus|hence)\s+/i, connector: '. ' },
        { pattern: /,\s+(and|but|or|yet|so)\s+/i, connector: '. ' },
        { pattern: /;\s+/g, connector: '. ' },
        { pattern: /,\s+which\s+/i, connector: '. This ' },
        { pattern: /,\s+that\s+/i, connector: '. This ' }
    ];

    for (const breakPoint of breakPoints) {
        if (sentence.match(breakPoint.pattern)) {
            const parts = sentence.split(breakPoint.pattern);
            if (parts.length === 2) {
                return [parts[0].trim() + '.', parts[1].trim()];
            }
        }
    }

    return [sentence];
}

/**
 * Add sentence variety through structural changes
 */
function addSentenceVariety(sentence) {
    // TEMPORARILY DISABLED to fix word shifting bug
    // The issue is that adding introductory phrases shifts word positions
    // and causes subsequent word replacements to be misaligned

    // TODO: Fix this by properly coordinating with word replacement phases
    // or by doing sentence-level additions after all word-level replacements

    return sentence; // Return unchanged for now

    /* ORIGINAL CODE - DISABLED
    const varietyPatterns = [
        {
            // Add introductory phrases - FIXED: Properly insert at beginning without replacing
            pattern: /^(.+)$/,
            replacements: ['Notably, $1', 'Interestingly, $1', 'Remarkably, $1', 'Importantly, $1']
        },
        {
            // Convert statements to questions and back
            pattern: /^(.+)\?$/,
            replacements: ['$1.', 'Consider this: $1.']
        }
    ];

    for (const pattern of varietyPatterns) {
        if (sentence.match(pattern.pattern)) {
            const replacement = pattern.replacements[Math.floor(Math.random() * pattern.replacements.length)];
            return sentence.replace(pattern.pattern, replacement);
        }
    }

    return sentence;
    */
}

/**
 * Disrupt syntactic patterns based on content type
 */
function disruptSyntacticPatterns(sentence, contentTypeAnalysis) {
    let result = sentence;

    // Avoid formal patterns in business content
    if (contentTypeAnalysis.type === 'business') {
        result = result.replace(/\b(utilize|implement|facilitate|optimize|leverage)\b/gi, (match) => {
            const alternatives = {
                'utilize': 'use',
                'implement': 'put in place',
                'facilitate': 'help',
                'optimize': 'improve',
                'leverage': 'use'
            };
            return alternatives[match.toLowerCase()] || match;
        });
    }

    // Break up predictable sentence structures
    result = result.replace(/^(The|This|These|Those)\s+(fact|reality|truth|issue|problem|challenge|opportunity|benefit|advantage|disadvantage|aspect|element|component|factor|consideration)\s+(is|are)\s+that\s+/i,
        (match, article, noun, verb) => {
            const alternatives = [
                `${article} ${noun} ${verb} `,
                `What's important is that `,
                `Here's the thing: `,
                `Consider this: `
            ];
            return alternatives[Math.floor(Math.random() * alternatives.length)];
        });

    return result;
}

/**
 * Advanced context-aware synonym replacement with enhanced semantic preservation
 */
function advancedContextAwareSynonymReplacement(text, aggressiveness, protectedSentences = [], contentTypeAnalysis = {}) {
    let result = text;

    // Check if text has line breaks that should be preserved
    const hasLineBreaks = text.includes('\n');

    if (hasLineBreaks) {
        // Process line by line to preserve formatting
        return processTextWithLineBreaks(text, (sentence) =>
            processAdvancedSynonymReplacement(sentence, aggressiveness, protectedSentences, contentTypeAnalysis)
        );
    }

    const sentences = result.split(/(?<=[.!?])\s+/);
    const processedSentences = sentences.map(sentence => {
        if (protectedSentences.includes(sentence.trim())) {
            return sentence;
        }
        return processAdvancedSynonymReplacement(sentence, aggressiveness, protectedSentences, contentTypeAnalysis);
    });

    return processedSentences.join(' ');
}

/**
 * Process advanced synonym replacement for individual sentences
 */
function processAdvancedSynonymReplacement(sentence, aggressiveness, protectedSentences, contentTypeAnalysis) {
    // Enhanced contextual synonyms with domain-specific adaptations
    const domainSynonyms = getDomainSpecificSynonyms(contentTypeAnalysis.type);
    const contextualSynonyms = {
        ...getBaseSynonyms(),
        ...domainSynonyms
    };

    // Process words with higher replacement probability for better detection avoidance
    const words = sentence.split(/(\s+|[^\w\s])/);
    const processedWords = words.map((word, index) => {
        const cleanWord = word.toLowerCase().replace(/[^\w]/g, '');

        if (cleanWord.length < 3 || !cleanWord.match(/^[a-z]+$/)) {
            return word;
        }

        if (contextualSynonyms[cleanWord] && Math.random() < Math.min(0.9, aggressiveness * 0.8)) { // Further increased for high aggressiveness
            const synonymGroup = contextualSynonyms[cleanWord];
            const context = getWordContext(words, index, 5); // Increased context window

            // Enhanced context-aware selection
            let synonyms = selectContextualSynonyms(synonymGroup, context, contentTypeAnalysis);

            if (synonyms.length > 0) {
                const chosen = synonyms[Math.floor(Math.random() * synonyms.length)];
                return preserveCase(word, chosen);
            }
        }

        return word;
    });

    return processedWords.join('');
}

/**
 * Get domain-specific synonyms based on content type
 */
function getDomainSpecificSynonyms(contentType) {
    const domainSynonyms = {
        'business': {
            'strategy': { formal: ['approach', 'plan'], casual: ['game plan', 'way forward'], context: 'business|company' },
            'revenue': { formal: ['income', 'earnings'], casual: ['money coming in', 'sales'], context: 'financial|profit' },
            'optimize': { formal: ['enhance', 'improve'], casual: ['make better', 'fine-tune'], context: 'process|system' },
            'leverage': { formal: ['utilize', 'employ'], casual: ['use', 'tap into'], context: 'resource|advantage' }
        },
        'technical': {
            'implement': { formal: ['deploy', 'execute'], casual: ['put in place', 'set up'], context: 'system|solution' },
            'configure': { formal: ['set up', 'establish'], casual: ['arrange', 'organize'], context: 'system|settings' },
            'optimize': { formal: ['enhance', 'refine'], casual: ['improve', 'tune up'], context: 'performance|code' }
        },
        'academic': {
            'demonstrate': { formal: ['show', 'illustrate'], casual: ['prove', 'make clear'], context: 'research|study' },
            'analyze': { formal: ['examine', 'investigate'], casual: ['look at', 'study'], context: 'data|results' },
            'significant': { formal: ['important', 'notable'], casual: ['big', 'major'], context: 'finding|result' }
        }
    };

    return domainSynonyms[contentType] || {};
}

/**
 * Get base synonyms for general use
 */
function getBaseSynonyms() {
    return {
        // Verbs - context matters greatly
        'utilize': {
            formal: ['employ', 'apply'],
            casual: ['use', 'try'],
            context: 'tool|method|approach'
        },
        'demonstrate': {
            formal: ['illustrate', 'exhibit'],
            casual: ['show', 'prove'],
            context: 'example|evidence|proof'
        },
        'implement': {
            formal: ['execute', 'deploy'],
            casual: ['do', 'put in place', 'set up'],
            context: 'plan|strategy|solution'
        },
        // Adjectives - tone-sensitive
        'significant': {
            formal: ['substantial', 'considerable'],
            casual: ['big', 'major', 'important'],
            context: 'impact|change|difference'
        },
        'comprehensive': {
            formal: ['thorough', 'extensive'],
            casual: ['complete', 'full', 'detailed'],
            context: 'analysis|study|review'
        },
        'facilitate': {
            formal: ['enable', 'support'],
            casual: ['help', 'make easier'],
            context: 'process|development'
        },
        'enhance': {
            formal: ['improve', 'augment'],
            casual: ['boost', 'make better'],
            context: 'performance|quality'
        }
    };
}

/**
 * Select contextual synonyms based on context and content type
 */
function selectContextualSynonyms(synonymGroup, context, contentTypeAnalysis) {
    // Choose synonym based on context and content type
    let synonyms = [];

    if (synonymGroup.context && context.includes(synonymGroup.context)) {
        synonyms = [...synonymGroup.formal, ...synonymGroup.casual];
    } else if (contentTypeAnalysis.type === 'formal_document' || contentTypeAnalysis.type === 'academic') {
        synonyms = synonymGroup.formal || [];
    } else if (contentTypeAnalysis.type === 'business') {
        // Mix formal and casual for business content
        synonyms = [...(synonymGroup.formal || []), ...(synonymGroup.casual || [])];
    } else {
        synonyms = synonymGroup.casual || synonymGroup.formal || [];
    }

    return synonyms;
}

/**
 * Sophisticated human writing pattern injection with enhanced authenticity
 */
function sophisticatedHumanPatternInjection(text, aggressiveness, hesitationFrequency, protectedSentences = [], contentTypeAnalysis = {}) {
    let result = text;

    // 1. Add sophisticated hesitation and self-correction patterns
    result = addSophisticatedHesitationPatterns(result, aggressiveness, hesitationFrequency, protectedSentences, contentTypeAnalysis);

    // 2. Inject authentic conversational elements - TEMPORARILY DISABLED
    // This causes word shifting by adding elements at sentence beginnings
    // result = addAuthenticConversationalElements(result, aggressiveness * 0.6, protectedSentences, contentTypeAnalysis);

    // 3. Add natural human inconsistencies
    result = addNaturalHumanInconsistencies(result, aggressiveness * 0.4, contentTypeAnalysis);

    // 4. Inject human-like flow variations
    result = addHumanFlowVariations(result, aggressiveness * 0.5, contentTypeAnalysis);

    return result;
}

/**
 * Add sophisticated hesitation patterns that feel natural
 */
function addSophisticatedHesitationPatterns(text, aggressiveness, hesitationFrequency, protectedSentences, contentTypeAnalysis) {
    let result = text;

    // More sophisticated hesitation markers based on content type
    const hesitationMarkers = getContentTypeHesitationMarkers(contentTypeAnalysis.type);

    const sentences = result.split(/(?<=[.!?])\s+/);
    const processedSentences = sentences.map((sentence, index) => {
        if (protectedSentences.includes(sentence.trim()) || isInappropriateForHesitation(sentence)) {
            return sentence;
        }

        // Apply hesitation with controlled frequency - TEMPORARILY DISABLED
        // This is causing word shifting by adding markers at sentence beginnings
        /*
        if (Math.random() < hesitationFrequency * aggressiveness) {
            const marker = hesitationMarkers[Math.floor(Math.random() * hesitationMarkers.length)];

            // Vary placement for naturalness
            if (Math.random() < 0.7) {
                // Beginning of sentence
                return `${marker} ${sentence.charAt(0).toLowerCase() + sentence.slice(1)}`;
            } else {
                // Middle of sentence (after first clause)
                const commaIndex = sentence.indexOf(',');
                if (commaIndex > 0) {
                    return sentence.slice(0, commaIndex + 1) + ` ${marker},` + sentence.slice(commaIndex + 1);
                }
            }
        }
        */

        return sentence;
    });

    return processedSentences.join(' ');
}

/**
 * Get content-type specific hesitation markers
 */
function getContentTypeHesitationMarkers(contentType) {
    const markers = {
        'business': ['notably', 'importantly', 'specifically', 'particularly', 'essentially'],
        'academic': ['notably', 'importantly', 'specifically', 'remarkably', 'interestingly'],
        'technical': ['specifically', 'particularly', 'notably', 'importantly'],
        'formal_document': ['notably', 'importantly', 'specifically'],
        'general': ['actually', 'notably', 'importantly', 'specifically', 'particularly', 'essentially']
    };

    return markers[contentType] || markers['general'];
}

/**
 * Add authentic conversational elements based on content type
 */
function addAuthenticConversationalElements(text, aggressiveness, protectedSentences, contentTypeAnalysis) {
    let result = text;

    // Content-type appropriate conversational elements
    const conversationalElements = getConversationalElements(contentTypeAnalysis.type);

    const sentences = result.split(/(?<=[.!?])\s+/);
    const processedSentences = sentences.map(sentence => {
        if (protectedSentences.includes(sentence.trim()) || isInappropriateForHesitation(sentence)) {
            return sentence;
        }

        if (Math.random() < aggressiveness * 0.3) {
            const element = conversationalElements[Math.floor(Math.random() * conversationalElements.length)];

            // Add conversational connector
            if (Math.random() < 0.5) {
                return `${element} ${sentence.charAt(0).toLowerCase() + sentence.slice(1)}`;
            } else {
                // Add as transition between sentences
                return sentence + ` ${element}`;
            }
        }

        return sentence;
    });

    return processedSentences.join(' ');
}

/**
 * Get conversational elements appropriate for content type
 */
function getConversationalElements(contentType) {
    const elements = {
        'business': ['Here\'s the thing:', 'What\'s interesting is', 'The reality is', 'What we see is'],
        'academic': ['What\'s notable is', 'Interestingly enough', 'What emerges is', 'What becomes clear is'],
        'technical': ['What happens is', 'The key point is', 'What\'s important is', 'Here\'s what we find:'],
        'general': ['Here\'s the thing:', 'What\'s interesting is', 'The thing is', 'What we see is', 'The reality is']
    };

    return elements[contentType] || elements['general'];
}

/**
 * Add natural human inconsistencies
 */
function addNaturalHumanInconsistencies(text, aggressiveness, contentTypeAnalysis) {
    let result = text;

    // Avoid inconsistencies in formal documents
    if (contentTypeAnalysis.type === 'formal_document' || contentTypeAnalysis.type === 'academic') {
        return result;
    }

    // Add subtle inconsistencies that humans naturally have
    if (Math.random() < aggressiveness * 0.2) {
        // Occasional informal contractions in business content
        result = result.replace(/\b(do not|does not|did not|will not|would not|could not|should not)\b/gi, (match) => {
            const contractions = {
                'do not': 'don\'t',
                'does not': 'doesn\'t',
                'did not': 'didn\'t',
                'will not': 'won\'t',
                'would not': 'wouldn\'t',
                'could not': 'couldn\'t',
                'should not': 'shouldn\'t'
            };
            return Math.random() < 0.3 ? contractions[match.toLowerCase()] : match;
        });
    }

    return result;
}

/**
 * Add human-like flow variations
 */
function addHumanFlowVariations(text, aggressiveness, contentTypeAnalysis) {
    let result = text;

    // Add natural flow breaks and connectors
    const sentences = result.split(/(?<=[.!?])\s+/);
    const processedSentences = [];

    for (let i = 0; i < sentences.length; i++) {
        let sentence = sentences[i];

        // Occasionally combine short sentences
        if (Math.random() < aggressiveness * 0.2 && i < sentences.length - 1) {
            const nextSentence = sentences[i + 1];
            if (sentence.length < 60 && nextSentence.length < 60) {
                const connectors = ['and', 'but', 'so', 'yet'];
                const connector = connectors[Math.floor(Math.random() * connectors.length)];
                sentence = sentence.replace(/\.$/, '') + `, ${connector} ` +
                          nextSentence.charAt(0).toLowerCase() + nextSentence.slice(1);
                i++; // Skip next sentence
            }
        }

        processedSentences.push(sentence);
    }

    return processedSentences.join(' ');
}

/**
 * Advanced semantic disruption with AI pattern awareness
 */
function advancedSemanticDisruption(text, aggressiveness, protectedSentences, aiAnalysis) {
    let result = text;

    // Target specific AI patterns identified in the analysis
    if (aiAnalysis.patternTypes.formalTransitions) {
        result = disruptFormalTransitions(result, aggressiveness);
    }

    if (aiAnalysis.patternTypes.repetitiveStarters) {
        result = disruptRepetitiveStarters(result, aggressiveness);
    }

    if (aiAnalysis.patternTypes.structuralPredictability) {
        result = disruptStructuralPredictability(result, aggressiveness);
    }

    return result;
}

/**
 * Disrupt formal transitions that AI overuses
 */
function disruptFormalTransitions(text, aggressiveness) {
    const formalTransitions = {
        'furthermore': ['also', 'plus', 'what\'s more', 'on top of that'],
        'moreover': ['also', 'plus', 'what\'s more', 'beyond that'],
        'additionally': ['also', 'plus', 'what\'s more', 'on top of that'],
        'consequently': ['so', 'as a result', 'because of this', 'this means'],
        'therefore': ['so', 'as a result', 'because of this', 'this means'],
        'thus': ['so', 'as a result', 'this way', 'like this'],
        'hence': ['so', 'as a result', 'because of this', 'this means'],
        'however': ['but', 'yet', 'still', 'even so'],
        'nevertheless': ['but', 'yet', 'still', 'even so'],
        'nonetheless': ['but', 'yet', 'still', 'even so']
    };

    Object.entries(formalTransitions).forEach(([formal, alternatives]) => {
        const pattern = new RegExp(`\\b${formal}\\b`, 'gi');
        text = text.replace(pattern, (match) => {
            if (Math.random() < Math.min(0.95, aggressiveness * 0.9)) { // Increased probability
                return alternatives[Math.floor(Math.random() * alternatives.length)];
            }
            return match;
        });
    });

    return text;
}

/**
 * Disrupt repetitive sentence starters
 */
function disruptRepetitiveStarters(text, aggressiveness) {
    const sentences = text.split(/(?<=[.!?])\s+/);
    const processedSentences = sentences.map(sentence => {
        // Target common AI sentence starters
        if (Math.random() < aggressiveness * 0.6) {
            sentence = sentence.replace(/^(The fact that|The reality is that|The truth is that|It is important to note that|It should be noted that)/i,
                (match) => {
                    const alternatives = [
                        'What\'s key is that',
                        'Here\'s what matters:',
                        'The thing is,',
                        'What we see is that',
                        'What happens is'
                    ];
                    return alternatives[Math.floor(Math.random() * alternatives.length)];
                });
        }
        return sentence;
    });

    return processedSentences.join(' ');
}

/**
 * Disrupt structural predictability
 */
function disruptStructuralPredictability(text, aggressiveness) {
    let result = text;

    // Break up predictable enumeration patterns
    if (Math.random() < aggressiveness * 0.5) {
        result = result.replace(/\b(first|second|third|fourth|fifth)\b/gi, (match, p1) => {
            const alternatives = {
                'first': ['to start', 'initially', 'to begin with'],
                'second': ['next', 'then', 'after that'],
                'third': ['also', 'another point', 'what\'s more'],
                'fourth': ['additionally', 'plus', 'beyond that'],
                'fifth': ['finally', 'lastly', 'to wrap up']
            };
            const alts = alternatives[p1.toLowerCase()];
            return alts ? alts[Math.floor(Math.random() * alts.length)] : match;
        });
    }

    return result;
}

/**
 * Mitigate specific AI patterns identified in analysis
 */
function mitigateSpecificAIPatterns(text, aiAnalysis, aggressiveness) {
    let result = text;

    // Apply targeted mitigation based on detected patterns
    Object.entries(aiAnalysis.patternTypes).forEach(([patternType, data]) => {
        if (data.count > 3) { // Only mitigate if pattern appears frequently
            switch (patternType) {
                case 'vocabularyFormality':
                    result = mitigateVocabularyFormality(result, aggressiveness);
                    break;
                case 'intensifierOverload':
                    result = mitigateIntensifierOverload(result, aggressiveness);
                    break;
                case 'hedgingClusters':
                    result = mitigateHedgingClusters(result, aggressiveness);
                    break;
                case 'passiveVoiceOveruse':
                    result = mitigatePassiveVoiceOveruse(result, aggressiveness);
                    break;
            }
        }
    });

    return result;
}

/**
 * Mitigate vocabulary formality overuse
 */
function mitigateVocabularyFormality(text, aggressiveness) {
    const formalToInformal = {
        'utilize': 'use',
        'implement': 'put in place',
        'demonstrate': 'show',
        'facilitate': 'help',
        'optimize': 'improve',
        'enhance': 'boost',
        'leverage': 'use',
        'establish': 'set up',
        'maintain': 'keep',
        'ensure': 'make sure',
        'provide': 'give',
        'develop': 'build',
        'create': 'make',
        'generate': 'create',
        'produce': 'make',
        'achieve': 'reach',
        'accomplish': 'do',
        'attain': 'get',
        'obtain': 'get',
        'acquire': 'get',
        'secure': 'get'
    };

    Object.entries(formalToInformal).forEach(([formal, informal]) => {
        const pattern = new RegExp(`\\b${formal}\\b`, 'gi');
        text = text.replace(pattern, (match) => {
            if (Math.random() < Math.min(0.9, aggressiveness * 0.8)) { // Increased probability
                return informal;
            }
            return match;
        });
    });

    return text;
}

/**
 * Mitigate intensifier overload
 */
function mitigateIntensifierOverload(text, aggressiveness) {
    const intensifiers = [
        'extremely', 'incredibly', 'remarkably', 'exceptionally', 'extraordinarily',
        'tremendously', 'enormously', 'immensely', 'vastly', 'hugely', 'massively',
        'significantly', 'substantially', 'considerably', 'absolutely', 'completely',
        'entirely', 'fully', 'totally', 'wholly', 'utterly', 'thoroughly',
        'comprehensively', 'extensively', 'perfectly', 'ideally', 'optimally'
    ];

    intensifiers.forEach(intensifier => {
        const pattern = new RegExp(`\\b${intensifier}\\s+`, 'gi');
        text = text.replace(pattern, (match) => {
            if (Math.random() < aggressiveness * 0.4) {
                return ''; // Remove intensifier
            }
            return match;
        });
    });

    return text;
}

/**
 * Mitigate hedging clusters
 */
function mitigateHedgingClusters(text, aggressiveness) {
    // Remove excessive hedging words when they cluster
    const hedgingWords = ['might', 'could', 'would', 'should', 'may', 'can', 'possibly', 'probably', 'likely', 'perhaps', 'maybe', 'potentially'];

    hedgingWords.forEach(word => {
        // Remove second occurrence in close proximity
        const pattern = new RegExp(`(\\b${word}\\b.*?\\b${word}\\b)`, 'gi');
        text = text.replace(pattern, (match) => {
            if (Math.random() < aggressiveness * 0.6) {
                return match.replace(new RegExp(`\\b${word}\\b`, 'i'), '');
            }
            return match;
        });
    });

    return text;
}

/**
 * Mitigate passive voice overuse
 */
function mitigatePassiveVoiceOveruse(text, aggressiveness) {
    // This is handled by the convertPassiveToActive function in sentence restructuring
    // Additional mitigation can be added here if needed
    return text;
}

/**
 * Process technical content with term preservation
 */
function processTechnicalContent(text, aggressiveness, contentTypeAnalysis, documentAnalysis) {
    // Reduce aggressiveness for technical content to preserve accuracy
    const adjustedAggressiveness = aggressiveness * 0.7;

    // Use standard processing but with technical term protection
    const protectedSentences = identifyProtectedSentences(text, documentAnalysis);
    const textAnalysis = analyzeTextStructure(text);

    let result = text;

    // Apply lighter processing for technical content
    result = advancedSentenceRestructuring(result, textAnalysis, adjustedAggressiveness, protectedSentences);
    result = contextAwareSynonymReplacement(result, adjustedAggressiveness * 0.5, protectedSentences); // Reduced synonym replacement
    result = enhancePerplexityAndBurstiness(result, textAnalysis, adjustedAggressiveness, protectedSentences);

    // Skip aggressive human pattern injection for technical content
    const hesitationFrequency = calculateHesitationFrequency(documentAnalysis) * 0.3; // Reduced hesitation
    result = injectHumanWritingPatterns(result, adjustedAggressiveness * 0.5, hesitationFrequency, protectedSentences);

    return result;
}

/**
 * Preserve paragraph structure when processing formatted content aggressively
 */
function preserveParagraphStructure(originalText, processedText) {
    const originalParagraphs = originalText.split('\n\n');
    const processedSentences = processedText.split(/(?<=[.!?])\s+/);

    if (originalParagraphs.length <= 1) {
        return processedText; // No paragraph structure to preserve
    }

    // Reconstruct paragraphs by distributing processed sentences
    const result = [];
    let sentenceIndex = 0;

    for (let i = 0; i < originalParagraphs.length; i++) {
        const originalParagraph = originalParagraphs[i].trim();
        if (!originalParagraph) continue;

        // Count sentences in original paragraph
        const originalSentences = originalParagraph.split(/(?<=[.!?])\s+/);
        const sentenceCount = originalSentences.length;

        // Take corresponding processed sentences
        const paragraphSentences = processedSentences.slice(sentenceIndex, sentenceIndex + sentenceCount);
        sentenceIndex += sentenceCount;

        // Join sentences for this paragraph
        result.push(paragraphSentences.join(' '));
    }

    return result.join('\n\n');
}

/**
 * Adds conversational elements - with context awareness
 */
function addConversationalElements(text, aggressiveness, protectedSentences = []) {
    let result = text;
    const sentences = result.split(/(?<=[.!?])\s+/);

    const processedSentences = sentences.map((sentence, index) => {
        // Skip protected sentences
        if (protectedSentences.includes(index)) {
            return sentence;
        }

        // Skip inappropriate content
        if (isInappropriateForHesitation(sentence)) {
            return sentence;
        }

        let processedSentence = sentence;

        // Very conservative conversational markers (max 2% chance)
        if (Math.random() < aggressiveness * 0.02 && sentence.length > 30) {
            const conversationalMarkers = [
                { pattern: /\b(This|That)\s+(is|was)\s+/gi, replacements: ['$1 $2 basically ', '$1 $2 essentially '] }
            ];

            conversationalMarkers.forEach(marker => {
                processedSentence = processedSentence.replace(marker.pattern, (_match, ...groups) => {
                    const replacement = marker.replacements[Math.floor(Math.random() * marker.replacements.length)];
                    return replacement.replace(/\$(\d+)/g, (_, num) => groups[parseInt(num) - 1] || '');
                });
            });
        }

        return processedSentence;
    });

    return processedSentences.join(' ');
}

/**
 * Adds human-like tangents and asides - very conservative
 */
function addHumanTangents(text, aggressiveness, protectedSentences = []) {
    let result = text;
    const sentences = result.split(/(?<=[.!?])\s+/);

    const tangentMarkers = [
        ' (by the way)',
        ' (worth noting)'
    ];

    const enhancedSentences = sentences.map((sentence, index) => {
        // Skip protected sentences
        if (protectedSentences.includes(index)) {
            return sentence;
        }

        // Skip inappropriate content
        if (isInappropriateForHesitation(sentence)) {
            return sentence;
        }

        // Very low frequency for tangents (max 1% chance)
        if (Math.random() < aggressiveness * 0.01 && sentence.length > 50) {
            const tangent = tangentMarkers[Math.floor(Math.random() * tangentMarkers.length)];
            const insertPoint = Math.floor(sentence.length * 0.6);
            return sentence.slice(0, insertPoint) + tangent + sentence.slice(insertPoint);
        }
        return sentence;
    });

    return enhancedSentences.join(' ');
}

/**
 * Adds personal perspective markers - conservative approach
 */
function addPersonalPerspective(text, aggressiveness, protectedSentences = []) {
    let result = text;

    const perspectiveMarkers = [
        'I think ',
        'It seems ',
        'Personally, '
    ];

    const sentences = result.split(/(?<=[.!?])\s+/);
    const enhancedSentences = sentences.map((sentence, index) => {
        // Skip protected sentences
        if (protectedSentences.includes(index)) {
            return sentence;
        }

        // Skip inappropriate content
        if (isInappropriateForHesitation(sentence)) {
            return sentence;
        }

        // Very conservative frequency (max 2% chance)
        if (Math.random() < aggressiveness * 0.02 && sentence.length > 30 && index > 0) {
            const marker = perspectiveMarkers[Math.floor(Math.random() * perspectiveMarkers.length)];
            return marker + sentence.charAt(0).toLowerCase() + sentence.slice(1);
        }
        return sentence;
    });

    return enhancedSentences.join(' ');
}

/**
 * Subtle coherence disruption - breaks AI's perfect logical flow
 */
function subtleCoherenceDisruption(text, aggressiveness, protectedSentences = []) {
    let result = text;

    // 1. Occasionally break logical transitions
    result = breakLogicalTransitions(result, aggressiveness, protectedSentences);

    // 2. Add human-like topic drift (very conservative)
    result = addTopicDrift(result, aggressiveness * 0.2, protectedSentences);

    // 3. Insert human-like contradictions and corrections (very conservative)
    result = addHumanContradictions(result, aggressiveness * 0.1, protectedSentences);

    return result;
}

/**
 * Breaks overly logical transitions
 */
function breakLogicalTransitions(text, aggressiveness, protectedSentences = []) {
    let result = text;

    const logicalConnectors = [
        { pattern: /\bTherefore,\s*/gi, replacements: ['So, ', 'Thus, '] },
        { pattern: /\bConsequently,\s*/gi, replacements: ['So, ', 'Then, '] },
        { pattern: /\bAs a result,\s*/gi, replacements: ['So, ', 'Because of this, '] }
    ];

    logicalConnectors.forEach(connector => {
        result = result.replace(connector.pattern, (match, offset) => {
            // Check if this match is in a protected sentence
            const beforeMatch = text.substring(0, offset);
            const sentenceIndex = (beforeMatch.match(/[.!?]/g) || []).length;

            if (protectedSentences.includes(sentenceIndex)) {
                return match;
            }

            if (Math.random() < aggressiveness * 0.2) { // Reduced frequency
                const replacement = connector.replacements[Math.floor(Math.random() * connector.replacements.length)];
                return replacement;
            }
            return match;
        });
    });

    return result;
}

/**
 * Adds subtle topic drift - very conservative
 */
function addTopicDrift(text, aggressiveness, protectedSentences = []) {
    let result = text;
    const sentences = result.split(/(?<=[.!?])\s+/);

    const driftMarkers = [
        'On a related note, '
    ];

    const enhancedSentences = sentences.map((sentence, index) => {
        // Skip protected sentences
        if (protectedSentences.includes(index)) {
            return sentence;
        }

        // Skip inappropriate content
        if (isInappropriateForHesitation(sentence)) {
            return sentence;
        }

        // Very low frequency for topic drift (max 0.5% chance)
        if (Math.random() < aggressiveness * 0.005 && index > 2 && sentence.length > 30) {
            const drift = driftMarkers[Math.floor(Math.random() * driftMarkers.length)];
            return drift + sentence.charAt(0).toLowerCase() + sentence.slice(1);
        }
        return sentence;
    });

    return enhancedSentences.join(' ');
}

/**
 * Adds human-like contradictions and corrections - very conservative
 */
function addHumanContradictions(text, aggressiveness, protectedSentences = []) {
    let result = text;

    const contradictionPatterns = [
        'Let me rephrase that - '
    ];

    const sentences = result.split(/(?<=[.!?])\s+/);
    const enhancedSentences = sentences.map((sentence, index) => {
        // Skip protected sentences
        if (protectedSentences.includes(index)) {
            return sentence;
        }

        // Skip inappropriate content
        if (isInappropriateForHesitation(sentence)) {
            return sentence;
        }

        // Extremely low frequency for contradictions (max 0.2% chance)
        if (Math.random() < aggressiveness * 0.002 && index > 1 && sentence.length > 40) {
            const contradiction = contradictionPatterns[Math.floor(Math.random() * contradictionPatterns.length)];
            return contradiction + sentence.charAt(0).toLowerCase() + sentence.slice(1);
        }
        return sentence;
    });

    return enhancedSentences.join(' ');
}

/**
 * Final polish and quality check
 */
function finalPolishAndQualityCheck(text, _maintainTone, documentAnalysis) {
    let result = text;

    // 1. Clean up any formatting issues (preserve structure for formal docs)
    result = cleanupFormatting(result, documentAnalysis);

    // 2. Ensure readability is maintained (skip for formatted documents)
    if (!documentAnalysis.preserveFormatting) {
        result = ensureReadability(result);
    }

    // 3. Final human touch additions (very conservative for formal docs)
    if (!documentAnalysis.hasFormalStructure) {
        result = addFinalHumanTouches(result);
    }

    // 4. Quality validation with enhanced checks
    const quality = validateQuality(result, documentAnalysis);
    if (!quality.isAcceptable) {
        console.warn('Quality issues detected:', quality.issues);
        // Apply conservative fixes if quality is poor
        result = applyConservativeFixes(result, documentAnalysis);
    }

    return result;
}

/**
 * Cleans up formatting issues while preserving document structure
 */
function cleanupFormatting(text, documentAnalysis) {
    if (documentAnalysis && documentAnalysis.preserveFormatting) {
        // For formal documents, preserve line breaks and only clean up basic issues
        return text
            .replace(/[ \t]+/g, ' ') // Multiple spaces/tabs to single space (but preserve newlines)
            .replace(/[ \t]+([.!?])/g, '$1') // Remove space before punctuation (but not newlines)
            .replace(/([.!?])[ \t]*([.!?])/g, '$1 $2') // Fix multiple punctuation (but not newlines)
            .replace(/[ \t]+,/g, ',') // Remove space before comma (but not newlines)
            .replace(/,([^\s])/g, ', $1') // Add space after comma
            .replace(/^[ \t]+|[ \t]+$/gm, ''); // Trim each line but preserve line breaks
    } else {
        // For regular text, standard cleanup
        return text
            .replace(/\s+/g, ' ') // Multiple spaces to single
            .replace(/\s+([.!?])/g, '$1') // Remove space before punctuation
            .replace(/([.!?])\s*([.!?])/g, '$1 ') // Fix multiple punctuation
            .replace(/\s+,/g, ',') // Remove space before comma
            .replace(/,([^\s])/g, ', $1') // Add space after comma
            .trim();
    }
}

/**
 * Ensures readability is maintained
 */
function ensureReadability(text) {
    let result = text;

    // Check for overly long sentences and break them
    const sentences = result.split(/(?<=[.!?])\s+/);
    const readableSentences = sentences.map(sentence => {
        if (sentence.length > 150) {
            // Find a good breaking point
            const breakPoints = [', and ', ', but ', ', so ', '; '];
            for (const breakPoint of breakPoints) {
                const index = sentence.indexOf(breakPoint, 60);
                if (index > 0 && index < sentence.length - 30) {
                    const firstPart = sentence.substring(0, index).trim() + '.';
                    const secondPart = sentence.substring(index + breakPoint.length).trim();
                    return firstPart + ' ' + secondPart.charAt(0).toUpperCase() + secondPart.slice(1);
                }
            }
        }
        return sentence;
    });

    return readableSentences.join(' ');
}

/**
 * Adds final human touches
 */
function addFinalHumanTouches(text) {
    let result = text;

    // Add occasional emphasis
    result = result.replace(/\b(really|very|quite)\s+(\w+)/gi, (match, intensifier, word) => {
        if (Math.random() > 0.9) {
            return `${intensifier}, ${intensifier} ${word}`;
        }
        return match;
    });

    // Add occasional informal contractions
    const informalContractions = {
        'going to': "gonna",
        'want to': "wanna",
        'have to': "gotta",
        'out of': "outta"
    };

    Object.entries(informalContractions).forEach(([formal, informal]) => {
        const regex = new RegExp(`\\b${formal}\\b`, 'gi');
        result = result.replace(regex, (match) => {
            if (Math.random() > 0.95) { // Very rarely
                return preserveCase(match, informal);
            }
            return match;
        });
    });

    return result;
}

/**
 * Validates text quality with enhanced checks
 */
function validateQuality(text, documentAnalysis) {
    const issues = [];

    // Check for basic grammar issues
    if (text.includes('..')) issues.push('Multiple periods found');
    if (text.includes('  ') && !documentAnalysis?.preserveFormatting) issues.push('Multiple spaces found');
    if (text.match(/[a-z]\.[A-Z]/)) issues.push('Missing space after period');

    // Check for readability
    const sentences = text.split(/(?<=[.!?])\s+/);
    const avgLength = sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length;
    if (avgLength > 120) issues.push('Average sentence length too high');

    // Check for excessive hesitation markers
    const hesitationMarkers = ['well,', 'so,', 'actually,', 'listen,', 'look,'];
    let hesitationCount = 0;
    hesitationMarkers.forEach(marker => {
        hesitationCount += (text.toLowerCase().match(new RegExp(`\\b${marker}`, 'g')) || []).length;
    });

    // More strict limits for hesitation markers
    const maxHesitationRatio = documentAnalysis?.hasFormalStructure ? 0.02 : 0.05; // 2% for formal, 5% for casual
    if (hesitationCount > sentences.length * maxHesitationRatio) {
        issues.push('Too many hesitation markers');
    }

    // Check for inappropriate hesitation in formal content
    if (documentAnalysis?.hasFormalStructure) {
        const inappropriateHesitations = text.match(/\b(listen|look),\s*[a-z]/gi);
        if (inappropriateHesitations && inappropriateHesitations.length > 0) {
            issues.push('Inappropriate hesitation markers in formal content');
        }
    }

    return {
        isAcceptable: issues.length === 0,
        issues: issues
    };
}

/**
 * Applies conservative fixes if quality is poor
 */
function applyConservativeFixes(text, documentAnalysis) {
    let result = text;

    // Remove excessive hesitation markers
    const excessiveHesitations = ['\\blisten,\\s*', '\\blook,\\s*', '\\bwell,\\s*', '\\bso,\\s*'];
    excessiveHesitations.forEach(pattern => {
        result = result.replace(new RegExp(pattern, 'gi'), '');
    });

    // Remove parenthetical remarks if too many
    const parentheticalCount = (result.match(/\([^)]*\)/g) || []).length;
    const sentences = result.split(/(?<=[.!?])\s+/);
    if (parentheticalCount > sentences.length * 0.1) {
        result = result.replace(/\s*\([^)]*\)\s*/g, ' ');
    }

    // Clean up spaces appropriately
    if (documentAnalysis?.preserveFormatting) {
        result = result.replace(/[ \t]+/g, ' '); // Preserve line breaks
    } else {
        result = result.replace(/\s+/g, ' ').trim(); // Standard cleanup
    }

    return result;
}

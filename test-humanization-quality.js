// Test the quality and extent of humanization changes
import { humanizeText } from './src/services/humaneyesService.js';

const testText = `Artificial intelligence has revolutionized numerous industries and continues to demonstrate significant potential for future applications. Furthermore, it is important to note that machine learning algorithms can effectively analyze vast amounts of data to identify patterns and make predictions. Additionally, these systems utilize sophisticated neural networks to process information in ways that mimic human cognitive processes.

Moreover, the implementation of AI technologies has resulted in substantial improvements in efficiency and accuracy across various sectors. It is worth mentioning that organizations are increasingly leveraging these capabilities to optimize their operations and enhance decision-making processes. Consequently, the integration of artificial intelligence represents a transformative shift in how businesses approach complex challenges.

In conclusion, the continued advancement of AI systems will undoubtedly lead to even more innovative solutions and applications. It should be noted that this technological evolution requires careful consideration of ethical implications and responsible development practices. Therefore, stakeholders must collaborate to ensure that artificial intelligence benefits society as a whole.`;

async function testHumanizationQuality() {
    console.log('🧪 Testing Humanization Quality and Changes...\n');

    console.log('📝 Original text:');
    console.log(testText);
    console.log('\n' + '='.repeat(80) + '\n');

    try {
        // Test different humanization methods
        const methods = [
            { name: 'Auto (Default)', method: 'auto' },
            { name: 'DeepSeek-R1 LLM', method: 'llm' },
            { name: 'Pattern-Based', method: 'pattern' },
            { name: 'NLTK Approach', method: 'nltk' },
            { name: 'Commercial Grade', method: 'commercial' }
        ];

        for (const testMethod of methods) {
            console.log(`\n🔬 Testing ${testMethod.name} Method:`);
            console.log('-'.repeat(50));

            const result = await humanizeText(testText, {
                aggressiveness: 0.8,
                maintainTone: true,
                targetDetection: 10,
                method: testMethod.method,
                fallbackEnabled: true
            });
        
        console.log('🔄 Humanized text:');
        console.log(data.modifiedText);
        console.log('\n' + '='.repeat(80) + '\n');
        
        // Analyze changes
        console.log('📊 Change Analysis:');
        
        const originalWords = testText.toLowerCase().split(/\s+/);
        const humanizedWords = data.modifiedText.toLowerCase().split(/\s+/);
        
        let changedWords = 0;
        let totalWords = originalWords.length;
        
        const changes = [];
        
        // Simple word-by-word comparison (not perfect but gives an idea)
        for (let i = 0; i < Math.min(originalWords.length, humanizedWords.length); i++) {
            if (originalWords[i] !== humanizedWords[i]) {
                changedWords++;
                changes.push({
                    position: i,
                    original: originalWords[i],
                    humanized: humanizedWords[i]
                });
            }
        }
        
        console.log(`Total words: ${totalWords}`);
        console.log(`Changed words: ${changedWords}`);
        console.log(`Change percentage: ${((changedWords / totalWords) * 100).toFixed(2)}%`);
        
        console.log('\n🔍 Specific Changes:');
        changes.slice(0, 20).forEach((change, index) => {
            console.log(`${index + 1}. "${change.original}" → "${change.humanized}"`);
        });
        
        if (changes.length > 20) {
            console.log(`... and ${changes.length - 20} more changes`);
        }
        
        // Check for common AI patterns that should be humanized
        const aiPatterns = [
            'furthermore',
            'additionally', 
            'moreover',
            'it is important to note',
            'it should be noted',
            'it is worth mentioning',
            'consequently',
            'in conclusion',
            'therefore',
            'undoubtedly',
            'substantial',
            'significant',
            'numerous',
            'various',
            'sophisticated',
            'effectively',
            'utilize',
            'implement',
            'demonstrate',
            'revolutionized'
        ];
        
        console.log('\n🤖 AI Pattern Analysis:');
        const originalLower = testText.toLowerCase();
        const humanizedLower = data.modifiedText.toLowerCase();
        
        let patternsRemaining = 0;
        let patternsChanged = 0;
        
        aiPatterns.forEach(pattern => {
            const originalCount = (originalLower.match(new RegExp(pattern, 'g')) || []).length;
            const humanizedCount = (humanizedLower.match(new RegExp(pattern, 'g')) || []).length;
            
            if (originalCount > 0) {
                if (humanizedCount < originalCount) {
                    patternsChanged++;
                    console.log(`✅ "${pattern}": ${originalCount} → ${humanizedCount} (reduced)`);
                } else {
                    patternsRemaining++;
                    console.log(`❌ "${pattern}": ${originalCount} → ${humanizedCount} (unchanged)`);
                }
            }
        });
        
        console.log(`\nAI patterns changed: ${patternsChanged}`);
        console.log(`AI patterns remaining: ${patternsRemaining}`);
        console.log(`Pattern change rate: ${patternsChanged > 0 ? ((patternsChanged / (patternsChanged + patternsRemaining)) * 100).toFixed(2) : 0}%`);
        
        // Overall quality assessment
        console.log('\n🎯 Quality Assessment:');
        if (changedWords / totalWords < 0.05) {
            console.log('❌ POOR: Less than 5% of words changed - insufficient humanization');
        } else if (changedWords / totalWords < 0.15) {
            console.log('⚠️  FAIR: 5-15% of words changed - moderate humanization');
        } else if (changedWords / totalWords < 0.30) {
            console.log('✅ GOOD: 15-30% of words changed - substantial humanization');
        } else {
            console.log('🎉 EXCELLENT: 30%+ of words changed - extensive humanization');
        }
        
        if (data.detectionResult && data.detectionResult.score !== null) {
            console.log(`AI Detection Score: ${data.detectionResult.score}% (lower is better)`);
        }
        
    } catch (error) {
        console.error('❌ Error testing humanization:', error.message);
    }
}

testHumanizationQuality();
